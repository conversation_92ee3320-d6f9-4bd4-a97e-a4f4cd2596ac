import React, { useState } from "react";
import {
  Popover,
  <PERSON><PERSON><PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import { Paintbrush, Check } from "lucide-react";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Slider } from "@/components/ui/slider";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

const colorThemes = [
  { name: "Blue", primary: "#0066cc", secondary: "#f0f7ff" },
  { name: "<PERSON>", primary: "#10b981", secondary: "#ecfdf5" },
  { name: "Purple", primary: "#8b5cf6", secondary: "#f5f3ff" },
  { name: "Red", primary: "#ef4444", secondary: "#fef2f2" },
  { name: "<PERSON>", primary: "#f97316", secondary: "#fff7ed" },
  { name: "<PERSON><PERSON>", primary: "#14b8a6", secondary: "#f0fdfa" },
];

const fontOptions = [
  { name: "System", value: "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif" },
  { name: "Inter", value: "'Inter', sans-serif" },
  { name: "Poppins", value: "'Poppins', sans-serif" },
  { name: "Roboto", value: "'Roboto', sans-serif" },
];

export function ThemeCustomizer() {
  const [selectedTheme, setSelectedTheme] = useState(colorThemes[0]);
  const [fontSize, setFontSize] = useState(16);
  const [borderRadius, setBorderRadius] = useState(8);
  const [selectedFont, setSelectedFont] = useState(fontOptions[0]);

  const applyTheme = () => {
    document.documentElement.style.setProperty("--primary", selectedTheme.primary);
    document.documentElement.style.setProperty("--secondary", selectedTheme.secondary);
    document.documentElement.style.setProperty("--font-size-base", `${fontSize}px`);
    document.documentElement.style.setProperty("--radius", `${borderRadius}px`);
    document.documentElement.style.setProperty("--font-family", selectedFont.value);
    
    // Save preferences to localStorage
    localStorage.setItem("theme-preferences", JSON.stringify({
      theme: selectedTheme,
      fontSize,
      borderRadius,
      font: selectedFont,
    }));
  };

  // Load saved preferences on component mount
  React.useEffect(() => {
    const savedPreferences = localStorage.getItem("theme-preferences");
    if (savedPreferences) {
      const preferences = JSON.parse(savedPreferences);
      setSelectedTheme(preferences.theme);
      setFontSize(preferences.fontSize);
      setBorderRadius(preferences.borderRadius);
      setSelectedFont(preferences.font);
      
      // Apply saved preferences
      document.documentElement.style.setProperty("--primary", preferences.theme.primary);
      document.documentElement.style.setProperty("--secondary", preferences.theme.secondary);
      document.documentElement.style.setProperty("--font-size-base", `${preferences.fontSize}px`);
      document.documentElement.style.setProperty("--radius", `${preferences.borderRadius}px`);
      document.documentElement.style.setProperty("--font-family", preferences.font.value);
    }
  }, []);

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="icon" className="relative">
          <Paintbrush className="h-4 w-4" />
          <span className="sr-only">Customize theme</span>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80" align="end">
        <div className="space-y-4">
          <h4 className="font-medium text-sm">Customize Interface</h4>
          
          <Tabs defaultValue="colors">
            <TabsList className="w-full">
              <TabsTrigger value="colors" className="flex-1">Colors</TabsTrigger>
              <TabsTrigger value="typography" className="flex-1">Typography</TabsTrigger>
              <TabsTrigger value="layout" className="flex-1">Layout</TabsTrigger>
            </TabsList>
            
            <TabsContent value="colors" className="space-y-4 pt-4">
              <div className="grid grid-cols-3 gap-2">
                {colorThemes.map((theme) => (
                  <button
                    key={theme.name}
                    className={`relative h-12 rounded-md border-2 transition-all ${
                      selectedTheme.name === theme.name ? "border-primary" : "border-transparent"
                    }`}
                    style={{ backgroundColor: theme.primary }}
                    onClick={() => setSelectedTheme(theme)}
                  >
                    {selectedTheme.name === theme.name && (
                      <Check className="absolute inset-0 m-auto h-4 w-4 text-white" />
                    )}
                    <span className="sr-only">{theme.name}</span>
                  </button>
                ))}
              </div>
            </TabsContent>
            
            <TabsContent value="typography" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label>Font Family</Label>
                <RadioGroup 
                  value={selectedFont.name}
                  onValueChange={(value) => {
                    const font = fontOptions.find(f => f.name === value);
                    if (font) setSelectedFont(font);
                  }}
                >
                  {fontOptions.map((font) => (
                    <div key={font.name} className="flex items-center space-x-2">
                      <RadioGroupItem value={font.name} id={`font-${font.name}`} />
                      <Label htmlFor={`font-${font.name}`} style={{ fontFamily: font.value }}>
                        {font.name}
                      </Label>
                    </div>
                  ))}
                </RadioGroup>
              </div>
              
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label>Font Size</Label>
                  <span className="text-xs text-muted-foreground">{fontSize}px</span>
                </div>
                <Slider
                  value={[fontSize]}
                  min={12}
                  max={20}
                  step={1}
                  onValueChange={(value) => setFontSize(value[0])}
                />
              </div>
            </TabsContent>
            
            <TabsContent value="layout" className="space-y-4 pt-4">
              <div className="space-y-2">
                <div className="flex justify-between">
                  <Label>Border Radius</Label>
                  <span className="text-xs text-muted-foreground">{borderRadius}px</span>
                </div>
                <Slider
                  value={[borderRadius]}
                  min={0}
                  max={20}
                  step={1}
                  onValueChange={(value) => setBorderRadius(value[0])}
                />
              </div>
            </TabsContent>
          </Tabs>
          
          <Button className="w-full" onClick={applyTheme}>
            Apply Changes
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
}

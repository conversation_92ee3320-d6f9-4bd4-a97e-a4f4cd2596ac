/* Animation Utilities */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 5px rgba(var(--primary), 0.5); }
  50% { box-shadow: 0 0 20px rgba(var(--primary), 0.8); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-15px); }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-down {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in-left {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes color-cycle {
  0% { filter: hue-rotate(0deg); }
  100% { filter: hue-rotate(360deg); }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes expand {
  0% { width: 0; }
  100% { width: 100%; }
}

/* Animation Classes */
.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.2) 50%,
    rgba(255,255,255,0) 100%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-rotate {
  animation: rotate 10s linear infinite;
}

.animate-bounce {
  animation: bounce 2s ease-in-out infinite;
}

.animate-fade-in-up {
  animation: fade-in-up 0.6s ease-out forwards;
}

.animate-fade-in-down {
  animation: fade-in-down 0.6s ease-out forwards;
}

.animate-fade-in-left {
  animation: fade-in-left 0.6s ease-out forwards;
}

.animate-fade-in-right {
  animation: fade-in-right 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.5s ease-out forwards;
}

.animate-color-cycle {
  animation: color-cycle 10s linear infinite;
}

.animate-gradient-shift {
  background-size: 200% 200%;
  animation: gradient-shift 5s ease infinite;
}

.animate-expand {
  animation: expand 1.5s ease-out forwards;
}

/* Animation Delays */
.delay-100 { animation-delay: 100ms; }
.delay-200 { animation-delay: 200ms; }
.delay-300 { animation-delay: 300ms; }
.delay-400 { animation-delay: 400ms; }
.delay-500 { animation-delay: 500ms; }
.delay-600 { animation-delay: 600ms; }
.delay-700 { animation-delay: 700ms; }
.delay-800 { animation-delay: 800ms; }
.delay-900 { animation-delay: 900ms; }
.delay-1000 { animation-delay: 1000ms; }

/* Gradient Backgrounds */
.bg-gradient-blue-purple {
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
}

.bg-gradient-green-blue {
  background: linear-gradient(135deg, #10b981 0%, #3b82f6 100%);
}

.bg-gradient-orange-red {
  background: linear-gradient(135deg, #f97316 0%, #ef4444 100%);
}

.bg-gradient-pink-purple {
  background: linear-gradient(135deg, #ec4899 0%, #8b5cf6 100%);
}

.bg-gradient-cyan-blue {
  background: linear-gradient(135deg, #06b6d4 0%, #3b82f6 100%);
}

.bg-gradient-yellow-orange {
  background: linear-gradient(135deg, #facc15 0%, #f97316 100%);
}

/* Colorful Shadows */
.shadow-blue {
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.5);
}

.shadow-purple {
  box-shadow: 0 4px 14px rgba(139, 92, 246, 0.5);
}

.shadow-green {
  box-shadow: 0 4px 14px rgba(16, 185, 129, 0.5);
}

.shadow-pink {
  box-shadow: 0 4px 14px rgba(236, 72, 153, 0.5);
}

.shadow-orange {
  box-shadow: 0 4px 14px rgba(249, 115, 22, 0.5);
}

.shadow-red {
  box-shadow: 0 4px 14px rgba(239, 68, 68, 0.5);
}

/* Hover Effects */
.hover-scale {
  transition: transform 0.3s ease;
}
.hover-scale:hover {
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform 0.3s ease;
}
.hover-rotate:hover {
  transform: rotate(5deg);
}

.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.hover-glow {
  transition: box-shadow 0.3s ease;
}
.hover-glow:hover {
  box-shadow: 0 0 15px rgba(var(--primary), 0.6);
}

/* Colorful Borders */
.border-gradient-blue-purple {
  border: 2px solid transparent;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image:
    linear-gradient(to right, transparent, transparent),
    linear-gradient(135deg, #6366f1, #a855f7);
}

.border-gradient-green-blue {
  border: 2px solid transparent;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image:
    linear-gradient(to right, transparent, transparent),
    linear-gradient(135deg, #10b981, #3b82f6);
}

.border-gradient-orange-red {
  border: 2px solid transparent;
  background-clip: padding-box, border-box;
  background-origin: padding-box, border-box;
  background-image:
    linear-gradient(to right, transparent, transparent),
    linear-gradient(135deg, #f97316, #ef4444);
}

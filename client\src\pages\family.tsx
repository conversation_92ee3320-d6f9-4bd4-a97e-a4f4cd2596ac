import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Footer, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { FamilyMember, InsertFamilyMember } from "@shared/schema";
import { useToast } from "@/hooks/use-toast";
import {
  PlusCircle, User, FolderO<PERSON>, MoreVertical, User<PERSON><PERSON>, Edit, Trash2,
  Heart, Users, Shield, AlertCircle, Calendar, Sparkles, Activity
} from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { ColorfulButton } from "@/components/ui/colorful-button";
import { ColorfulCard } from "@/components/ui/colorful-card";
import { ParticleBackground } from "@/components/ui/particle-background";
import { InteractiveTooltip } from "@/components/ui/interactive-tooltip";

// Helper function to calculate age from date of birth
function calculateAge(dateOfBirth: string): number {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const m = today.getMonth() - birthDate.getMonth();
  if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  return age;
}

const familyMemberFormSchema = z.object({
  name: z.string().min(1, "Name is required"),
  relationship: z.string().min(1, "Relationship is required"),
  dateOfBirth: z.string().optional(),
  gender: z.string().optional(),
  bloodType: z.string().optional(),
  allergies: z.string().optional(),
  chronicConditions: z.string().optional(),
});

export default function Family() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [editingMember, setEditingMember] = useState<FamilyMember | null>(null);
  const [animateItems, setAnimateItems] = useState(false);
  const [colorMode, setColorMode] = useState<'blue' | 'purple' | 'green' | 'rainbow'>('green');
  const [hoveredMemberId, setHoveredMemberId] = useState<number | null>(null);

  useEffect(() => {
    // Animate items after a short delay for a staggered effect
    const timer = setTimeout(() => {
      setAnimateItems(true);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  const { data: familyMembers, isLoading } = useQuery<FamilyMember[]>({
    queryKey: ["/api/family-members"],
  });

  const form = useForm<z.infer<typeof familyMemberFormSchema>>({
    resolver: zodResolver(familyMemberFormSchema),
    defaultValues: {
      name: "",
      relationship: "",
      dateOfBirth: "",
      gender: "",
      bloodType: "",
      allergies: "",
      chronicConditions: "",
    },
  });

  const addMemberMutation = useMutation({
    mutationFn: (data: InsertFamilyMember) => {
      return apiRequest("POST", "/api/family-members", data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/family-members"] });
      setIsAddModalOpen(false);
      form.reset();
      toast({
        title: "Success",
        description: "Family member added successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to add family member: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const updateMemberMutation = useMutation({
    mutationFn: (data: { id: number, member: Partial<InsertFamilyMember> }) => {
      return apiRequest("PATCH", `/api/family-members/${data.id}`, data.member);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/family-members"] });
      setEditingMember(null);
      form.reset();
      toast({
        title: "Success",
        description: "Family member updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update family member: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const deleteMemberMutation = useMutation({
    mutationFn: (id: number) => {
      return apiRequest("DELETE", `/api/family-members/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/family-members"] });
      toast({
        title: "Success",
        description: "Family member removed successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to remove family member: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: z.infer<typeof familyMemberFormSchema>) => {
    if (editingMember) {
      updateMemberMutation.mutate({
        id: editingMember.id,
        member: data,
      });
    } else {
      addMemberMutation.mutate(data as InsertFamilyMember);
    }
  };

  const handleEditMember = (member: FamilyMember) => {
    setEditingMember(member);
    form.reset({
      name: member.name,
      relationship: member.relationship,
      dateOfBirth: member.dateOfBirth || "",
      gender: member.gender || "",
      bloodType: member.bloodType || "",
      allergies: member.allergies || "",
      chronicConditions: member.chronicConditions || "",
    });
  };

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-green-50 to-teal-100 dark:from-gray-800 dark:via-green-950 dark:to-teal-950 py-6 px-4 sm:px-6 lg:px-8 transition-all duration-500">
      {/* Particle Background */}
      <ParticleBackground
        colorScheme={colorMode}
        particleCount={80}
        speed={0.3}
        interactive={true}
        density={0.8}
      />

      {/* Animated background glow effects */}
      <div className="fixed inset-0 -z-5 overflow-hidden opacity-70 dark:opacity-40">
        <div className="absolute top-1/4 -left-20 w-72 h-72 bg-green-500/20 dark:bg-green-500/10 rounded-full filter blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 -right-20 w-72 h-72 bg-teal-500/20 dark:bg-teal-500/10 rounded-full filter blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* Page Header */}
      <div className="mb-8 relative group">
        <div className="absolute inset-0 bg-gradient-to-r from-green-600/5 via-teal-600/5 to-emerald-600/5 dark:from-green-400/10 dark:via-teal-400/10 dark:to-emerald-400/10 rounded-xl transform transition-all duration-300 group-hover:scale-105 group-hover:opacity-80"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-green-600/5 via-teal-600/5 to-emerald-600/5 dark:from-green-400/10 dark:via-teal-400/10 dark:to-emerald-400/10 rounded-xl blur-xl opacity-50 transform transition-all duration-300 group-hover:opacity-100"></div>

        <div className="relative bg-gradient-to-r from-green-500/10 via-teal-500/10 to-emerald-500/10 dark:from-green-500/20 dark:via-teal-500/20 dark:to-emerald-500/20 p-6 rounded-xl backdrop-blur-sm animate-fadeIn border border-white/10 dark:border-white/5 shadow-lg">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between">
            <div className="flex items-start">
              <div className="relative mr-4">
                <div className="absolute inset-0 bg-green-500/30 dark:bg-green-400/20 rounded-full filter blur-md animate-pulse-slow"></div>
                <Users className="h-10 w-10 text-green-600 dark:text-green-300 relative z-10 animate-float" />
              </div>

              <div className="flex-1">
                <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-teal-600 dark:from-green-300 dark:to-teal-300 animate-gradient-shift">
                  Family Vault
                </h1>
                <div className="h-1 w-0 bg-gradient-to-r from-green-500 to-teal-600 dark:from-green-400 dark:to-teal-400 mt-1 rounded-full animate-expand"></div>

                <p className="mt-3 text-base text-gray-700 dark:text-gray-200">
                  Manage your family members' health records in one secure place
                </p>
              </div>
            </div>

            <div className="mt-4 sm:mt-0">
              <ColorfulButton
                colorScheme="gradient-green-teal"
                glowEffect={true}
                hoverAnimation="scale"
                className="relative overflow-hidden group"
                onClick={() => {
                  form.reset();
                  setIsAddModalOpen(true);
                }}
              >
                <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-green-600/20 to-teal-600/20 dark:from-green-400/20 dark:to-teal-400/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                <span className="relative flex items-center">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add Family Member
                </span>
              </ColorfulButton>
            </div>
          </div>

          <div className="mt-4 flex flex-wrap gap-3">
            <div className="flex items-center space-x-2 bg-green-500/10 dark:bg-green-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.3s' }}>
              <div className="w-2 h-2 rounded-full bg-green-500 dark:bg-green-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-green-700 dark:text-green-300">Secure Storage</p>
            </div>

            <div className="flex items-center space-x-2 bg-teal-500/10 dark:bg-teal-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.5s' }}>
              <div className="w-2 h-2 rounded-full bg-teal-500 dark:bg-teal-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-teal-700 dark:text-teal-300">Health Tracking</p>
            </div>

            <div className="flex items-center space-x-2 bg-emerald-500/10 dark:bg-emerald-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.7s' }}>
              <div className="w-2 h-2 rounded-full bg-emerald-500 dark:bg-emerald-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-emerald-700 dark:text-emerald-300">Family-Centered Care</p>
            </div>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className={`transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: `${i * 100}ms` }}>
              <ColorfulCard
                colorScheme="gradient-green-teal"
                glowEffect={true}
                hoverAnimation="lift"
                className="overflow-hidden h-full"
              >
                <div className="animate-pulse">
                  <div className="p-4 flex items-center bg-gradient-to-r from-green-100/50 to-teal-50/50 dark:from-gray-800/80 dark:to-gray-900/80">
                    <div className="h-12 w-12 rounded-full bg-green-200 dark:bg-green-800/50"></div>
                    <div className="ml-4 flex-1">
                      <div className="h-5 w-32 bg-green-200 dark:bg-green-800/50 rounded-md"></div>
                      <div className="h-4 w-24 bg-green-100 dark:bg-green-900/50 rounded-md mt-2"></div>
                    </div>
                  </div>
                  <div className="border-t border-green-100 dark:border-green-800/30 px-4 py-4 bg-white/80 dark:bg-gray-800/80">
                    <div className="grid grid-cols-2 gap-x-4 gap-y-2">
                      <div className="col-span-1">
                        <div className="h-3 w-16 bg-green-100 dark:bg-green-900/50 rounded-md"></div>
                        <div className="h-4 w-12 bg-green-200 dark:bg-green-800/50 rounded-md mt-1"></div>
                      </div>
                      <div className="col-span-1">
                        <div className="h-3 w-16 bg-green-100 dark:bg-green-900/50 rounded-md"></div>
                        <div className="h-4 w-12 bg-green-200 dark:bg-green-800/50 rounded-md mt-1"></div>
                      </div>
                      <div className="col-span-2">
                        <div className="h-3 w-20 bg-green-100 dark:bg-green-900/50 rounded-md"></div>
                        <div className="h-4 w-32 bg-green-200 dark:bg-green-800/50 rounded-md mt-1"></div>
                      </div>
                    </div>
                  </div>
                  <div className="bg-green-50/80 dark:bg-gray-900/80 px-4 py-3 border-t border-green-100 dark:border-green-800/30">
                    <div className="h-8 w-full bg-green-200 dark:bg-green-800/50 rounded-md"></div>
                  </div>
                </div>
              </ColorfulCard>
            </div>
          ))}
        </div>
      ) : familyMembers && familyMembers.length > 0 ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          {familyMembers.map((member, index) => (
            <div
              key={member.id}
              className={`transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}
              style={{ transitionDelay: `${index * 100}ms` }}
              onMouseEnter={() => setHoveredMemberId(member.id)}
              onMouseLeave={() => setHoveredMemberId(null)}
            >
              <ColorfulCard
                colorScheme="gradient-green-teal"
                glowEffect={true}
                hoverAnimation="lift"
                className="overflow-hidden h-full"
              >
                <div className="p-4 flex items-center bg-gradient-to-r from-green-100/50 to-teal-50/50 dark:from-gray-800/80 dark:to-gray-900/80 relative">
                  {/* Animated glow effect on hover */}
                  {hoveredMemberId === member.id && (
                    <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-teal-500/5 dark:from-green-500/10 dark:to-teal-500/10 animate-pulse-slow"></div>
                  )}

                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-teal-500/20 dark:from-green-400/20 dark:to-teal-400/20 rounded-full blur-sm animate-pulse-slow"></div>
                    <Avatar className="h-14 w-14 border-2 border-white dark:border-gray-800 shadow-md relative z-10">
                      <AvatarImage src={member.avatarUrl || ""} alt={member.name} />
                      <AvatarFallback className="bg-gradient-to-br from-green-500 to-teal-500 dark:from-green-400 dark:to-teal-400 text-white">
                        {member.name.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                  </div>

                  <div className="ml-4 flex-1">
                    <h3 className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-teal-600 dark:from-green-300 dark:to-teal-300">
                      {member.name}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300 flex items-center">
                      <span>{member.relationship}</span>
                      {member.dateOfBirth && (
                        <>
                          <span className="mx-1">•</span>
                          <span className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1 text-green-500 dark:text-green-400" />
                            {calculateAge(member.dateOfBirth)} years
                          </span>
                        </>
                      )}
                    </p>
                  </div>

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0 text-gray-500 dark:text-gray-400 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20 transition-colors">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-gray-200 dark:border-gray-700">
                      <DropdownMenuItem
                        onClick={() => handleEditMember(member)}
                        className="text-gray-700 dark:text-gray-300 hover:text-green-600 dark:hover:text-green-400 hover:bg-green-50 dark:hover:bg-green-900/20"
                      >
                        <Edit className="mr-2 h-4 w-4" />
                        Edit
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
                        onClick={() => {
                          if (window.confirm(`Are you sure you want to remove ${member.name}?`)) {
                            deleteMemberMutation.mutate(member.id);
                          }
                        }}
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Remove
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="border-t border-green-100 dark:border-green-800/30 px-4 py-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                  <dl className="grid grid-cols-2 gap-x-4 gap-y-3">
                    <div className="col-span-1">
                      <dt className="text-xs font-medium text-green-600 dark:text-green-400 flex items-center">
                        <Heart className="h-3 w-3 mr-1" />
                        Blood Type
                      </dt>
                      <dd className="mt-1 text-sm text-gray-700 dark:text-gray-300 font-medium">
                        {member.bloodType || "—"}
                      </dd>
                    </div>
                    <div className="col-span-1">
                      <dt className="text-xs font-medium text-green-600 dark:text-green-400 flex items-center">
                        <User className="h-3 w-3 mr-1" />
                        Gender
                      </dt>
                      <dd className="mt-1 text-sm text-gray-700 dark:text-gray-300 font-medium">
                        {member.gender || "—"}
                      </dd>
                    </div>
                    <div className="col-span-2">
                      <dt className="text-xs font-medium text-green-600 dark:text-green-400 flex items-center">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Allergies
                      </dt>
                      <dd className="mt-1 text-sm text-gray-700 dark:text-gray-300">
                        {member.allergies ? (
                          <div className="flex flex-wrap gap-1">
                            {member.allergies.split(',').map((allergy, i) => (
                              <span key={i} className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
                                {allergy.trim()}
                              </span>
                            ))}
                          </div>
                        ) : (
                          <span className="text-gray-500 dark:text-gray-400">None</span>
                        )}
                      </dd>
                    </div>
                    <div className="col-span-2">
                      <dt className="text-xs font-medium text-green-600 dark:text-green-400 flex items-center">
                        <Activity className="h-3 w-3 mr-1" />
                        Chronic Conditions
                      </dt>
                      <dd className="mt-1 text-sm text-gray-700 dark:text-gray-300">
                        {member.chronicConditions ? (
                          <div className="flex flex-wrap gap-1">
                            {member.chronicConditions.split(',').map((condition, i) => (
                              <span key={i} className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300">
                                {condition.trim()}
                              </span>
                            ))}
                          </div>
                        ) : (
                          <span className="text-gray-500 dark:text-gray-400">None</span>
                        )}
                      </dd>
                    </div>
                  </dl>
                </div>

                <div className="bg-green-50/80 dark:bg-gray-900/80 px-4 py-3 border-t border-green-100 dark:border-green-800/30">
                  <ColorfulButton
                    colorScheme="gradient-green-teal"
                    className="w-full text-sm"
                    hoverAnimation="scale"
                  >
                    <FolderOpen className="mr-2 h-4 w-4" />
                    View Medical Records
                  </ColorfulButton>
                </div>
              </ColorfulCard>
            </div>
          ))}
        </div>
      ) : (
        <div className={`transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
          <ColorfulCard
            colorScheme="gradient-green-teal"
            glowEffect={true}
            hoverAnimation="lift"
            entranceAnimation="fade-up"
            className="overflow-hidden"
          >
            <div className="p-8 text-center bg-gradient-to-br from-green-100/70 to-teal-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm">
              <div className="flex flex-col items-center justify-center">
                <div className="relative mb-6">
                  <div className="absolute inset-0 bg-gradient-to-br from-green-500/30 to-teal-500/30 dark:from-green-400/20 dark:to-teal-400/20 rounded-full blur-xl animate-pulse-slow"></div>
                  <div className="relative z-10 bg-gradient-to-br from-green-500/10 to-teal-500/10 dark:from-green-400/10 dark:to-teal-400/10 p-4 rounded-full">
                    <Users className="h-20 w-20 text-green-600 dark:text-green-400" />
                  </div>
                </div>

                <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-teal-600 dark:from-green-300 dark:to-teal-300 mb-2">
                  No Family Members Yet
                </h3>

                <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md">
                  Add family members to manage their health records all in one place. Keep track of important medical information for your loved ones.
                </p>

                <div className="flex flex-wrap gap-4 justify-center mb-6">
                  <div className="flex items-center space-x-2 bg-green-100/70 dark:bg-green-900/30 px-3 py-1.5 rounded-full">
                    <Shield className="h-4 w-4 text-green-600 dark:text-green-400" />
                    <p className="text-xs font-medium text-green-700 dark:text-green-300">Secure Storage</p>
                  </div>

                  <div className="flex items-center space-x-2 bg-teal-100/70 dark:bg-teal-900/30 px-3 py-1.5 rounded-full">
                    <Heart className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                    <p className="text-xs font-medium text-teal-700 dark:text-teal-300">Health Tracking</p>
                  </div>

                  <div className="flex items-center space-x-2 bg-emerald-100/70 dark:bg-emerald-900/30 px-3 py-1.5 rounded-full">
                    <Users className="h-4 w-4 text-emerald-600 dark:text-emerald-400" />
                    <p className="text-xs font-medium text-emerald-700 dark:text-emerald-300">Family-Centered</p>
                  </div>
                </div>

                <ColorfulButton
                  colorScheme="gradient-green-teal"
                  glowEffect={true}
                  hoverAnimation="scale"
                  className="relative overflow-hidden group"
                  onClick={() => setIsAddModalOpen(true)}
                >
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-green-600/20 to-teal-600/20 dark:from-green-400/20 dark:to-teal-400/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                  <span className="relative flex items-center">
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Add Your First Family Member
                  </span>
                </ColorfulButton>
              </div>
            </div>
          </ColorfulCard>
        </div>
      )}

      {/* Add/Edit Family Member Modal */}
      <Dialog
        open={isAddModalOpen || !!editingMember}
        onOpenChange={(open) => {
          if (!open) {
            setIsAddModalOpen(false);
            setEditingMember(null);
          }
        }}
      >
        <DialogContent className="sm:max-w-[500px] bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-green-200 dark:border-green-800/30">
          <div className="absolute inset-0 bg-gradient-to-r from-green-500/5 to-teal-500/5 dark:from-green-500/10 dark:to-teal-500/10 rounded-lg -z-10"></div>

          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-teal-600 dark:from-green-300 dark:to-teal-300">
              {editingMember ? "Edit Family Member" : "Add Family Member"}
            </DialogTitle>
            <DialogDescription className="text-gray-600 dark:text-gray-300">
              {editingMember
                ? "Update information for your family member"
                : "Add a new member to your family vault"}
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="col-span-2 relative">
                  <Label htmlFor="name" className="text-gray-700 dark:text-gray-300">
                    Full Name <span className="text-red-500 dark:text-red-400">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      id="name"
                      placeholder="Enter full name"
                      className="border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500 dark:focus:ring-green-400 pl-10"
                      {...form.register("name")}
                    />
                    <User className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                  </div>
                  {form.formState.errors.name && (
                    <p className="text-sm text-red-500 dark:text-red-400 mt-1">{form.formState.errors.name.message}</p>
                  )}
                </div>

                <div className="relative">
                  <Label htmlFor="relationship" className="text-gray-700 dark:text-gray-300">
                    Relationship <span className="text-red-500 dark:text-red-400">*</span>
                  </Label>
                  <div className="relative">
                    <Input
                      id="relationship"
                      placeholder="e.g. Spouse, Child, Parent"
                      className="border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500 dark:focus:ring-green-400 pl-10"
                      {...form.register("relationship")}
                    />
                    <Users className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                  </div>
                  {form.formState.errors.relationship && (
                    <p className="text-sm text-red-500 dark:text-red-400 mt-1">{form.formState.errors.relationship.message}</p>
                  )}
                </div>

                <div className="relative">
                  <Label htmlFor="dateOfBirth" className="text-gray-700 dark:text-gray-300">
                    Date of Birth
                  </Label>
                  <div className="relative">
                    <Input
                      id="dateOfBirth"
                      type="date"
                      className="border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500 dark:focus:ring-green-400 pl-10"
                      {...form.register("dateOfBirth")}
                    />
                    <Calendar className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                  </div>
                </div>

                <div className="relative">
                  <Label htmlFor="gender" className="text-gray-700 dark:text-gray-300">
                    Gender
                  </Label>
                  <div className="relative">
                    <Input
                      id="gender"
                      placeholder="e.g. Male, Female, Other"
                      className="border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500 dark:focus:ring-green-400 pl-10"
                      {...form.register("gender")}
                    />
                    <User className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                  </div>
                </div>

                <div className="relative">
                  <Label htmlFor="bloodType" className="text-gray-700 dark:text-gray-300">
                    Blood Type
                  </Label>
                  <div className="relative">
                    <Input
                      id="bloodType"
                      placeholder="e.g. A+, B-, O+"
                      className="border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500 dark:focus:ring-green-400 pl-10"
                      {...form.register("bloodType")}
                    />
                    <Heart className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                  </div>
                </div>

                <div className="col-span-2 relative">
                  <Label htmlFor="allergies" className="text-gray-700 dark:text-gray-300">
                    Allergies
                  </Label>
                  <div className="relative">
                    <Input
                      id="allergies"
                      placeholder="e.g. Peanuts, Penicillin"
                      className="border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500 dark:focus:ring-green-400 pl-10"
                      {...form.register("allergies")}
                    />
                    <AlertCircle className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2 pointer-events-none" />
                  </div>
                </div>

                <div className="col-span-2 relative">
                  <Label htmlFor="chronicConditions" className="text-gray-700 dark:text-gray-300">
                    Chronic Conditions
                  </Label>
                  <div className="relative">
                    <Textarea
                      id="chronicConditions"
                      placeholder="e.g. Asthma, Diabetes"
                      className="border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-900/50 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500 dark:focus:ring-green-400 min-h-[80px] pl-10 pt-2"
                      {...form.register("chronicConditions")}
                    />
                    <Activity className="h-4 w-4 text-gray-400 dark:text-gray-500 absolute left-3 top-3 pointer-events-none" />
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter className="flex flex-col sm:flex-row gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsAddModalOpen(false);
                  setEditingMember(null);
                }}
                className="border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300"
              >
                Cancel
              </Button>

              <ColorfulButton
                type="submit"
                colorScheme="gradient-green-teal"
                glowEffect={true}
                hoverAnimation="scale"
                className="relative overflow-hidden"
                disabled={addMemberMutation.isPending || updateMemberMutation.isPending}
              >
                <span className="flex items-center">
                  {(addMemberMutation.isPending || updateMemberMutation.isPending) ? (
                    "Saving..."
                  ) : editingMember ? (
                    <>
                      <Edit className="mr-2 h-4 w-4" />
                      Update Member
                    </>
                  ) : (
                    <>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Add Member
                    </>
                  )}
                </span>
              </ColorfulButton>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Function removed - duplicate

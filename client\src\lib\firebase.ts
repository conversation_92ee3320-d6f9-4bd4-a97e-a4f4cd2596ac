// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth, GoogleAuthProvider, signInWithPopup, signOut, User } from "firebase/auth";

// Your web app's Firebase configuration
// For Firebase JS SDK v7.20.0 and later, measurementId is optional
const firebaseConfig = {
  apiKey: "AIzaSyDZt8n-Jq9MNQPkLIAcBgDCCJVSqyNY9Oc",
  authDomain: "medikey-auth.firebaseapp.com",
  projectId: "medikey-auth",
  storageBucket: "medikey-auth.appspot.com",
  messagingSenderId: "**********",
  appId: "1:**********:web:abcdef1234567890abcdef",
  measurementId: "G-ABCDEFGHIJ"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const auth = getAuth(app);
const googleProvider = new GoogleAuthProvider();

// Sign in with Google
export const signInWithGoogle = async () => {
  try {
    const result = await signInWithPopup(auth, googleProvider);
    console.log("Google sign-in successful:", result.user);
    
    // Store user in localStorage for the mock environment
    const user = {
      id: result.user.uid,
      username: result.user.displayName || result.user.email?.split('@')[0] || 'user',
      fullName: result.user.displayName || '',
      email: result.user.email || '',
      avatar: result.user.photoURL || undefined
    };
    
    localStorage.setItem('medikey_current_user', JSON.stringify(user));
    
    return user;
  } catch (error) {
    console.error("Google sign-in error:", error);
    throw error;
  }
};

// Sign out
export const signOutUser = async () => {
  try {
    await signOut(auth);
    console.log("Sign-out successful");
    
    // Clear user from localStorage
    localStorage.removeItem('medikey_current_user');
    
    return { message: 'Logout successful' };
  } catch (error) {
    console.error("Sign-out error:", error);
    throw error;
  }
};

// Get current user
export const getCurrentUser = (): User | null => {
  return auth.currentUser;
};

export { auth, googleProvider };

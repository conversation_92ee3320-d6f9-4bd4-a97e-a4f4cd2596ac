import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, <PERSON>Header } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface AnimatedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  hoverEffect?: "lift" | "glow" | "border" | "scale" | "none";
  clickEffect?: boolean;
  className?: string;
  contentClassName?: string;
  headerClassName?: string;
  footerClassName?: string;
}

export function AnimatedCard({
  children,
  header,
  footer,
  hoverEffect = "lift",
  clickEffect = true,
  className,
  contentClassName,
  headerClassName,
  footerClassName,
  ...props
}: AnimatedCardProps) {
  const [isPressed, setIsPressed] = useState(false);

  const getHoverClass = () => {
    switch (hoverEffect) {
      case "lift":
        return "hover:-translate-y-1 hover:shadow-lg";
      case "glow":
        return "hover:shadow-md hover:shadow-primary/25";
      case "border":
        return "hover:border-primary";
      case "scale":
        return "hover:scale-[1.02]";
      case "none":
        return "";
      default:
        return "hover:-translate-y-1 hover:shadow-lg";
    }
  };

  return (
    <Card
      className={cn(
        "transition-all duration-300 ease-in-out",
        getHoverClass(),
        clickEffect && isPressed ? "scale-[0.98] opacity-90" : "",
        className
      )}
      onMouseDown={() => clickEffect && setIsPressed(true)}
      onMouseUp={() => clickEffect && setIsPressed(false)}
      onMouseLeave={() => clickEffect && setIsPressed(false)}
      {...props}
    >
      {header && (
        <CardHeader className={cn("transition-colors duration-300", headerClassName)}>
          {header}
        </CardHeader>
      )}
      <CardContent className={cn("transition-colors duration-300", contentClassName)}>
        {children}
      </CardContent>
      {footer && (
        <CardFooter className={cn("transition-colors duration-300", footerClassName)}>
          {footer}
        </CardFooter>
      )}
    </Card>
  );
}

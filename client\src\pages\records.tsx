import { useState, useEffect } from "react";
import RecordList from "@/components/records/RecordList";
import RecordUploadModal from "@/components/records/RecordUploadModal";
import RecordViewer from "@/components/records/RecordViewer";
import { Button } from "@/components/ui/button";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { MedicalRecord } from "@shared/schema";
import { Plus, Search, Filter, FileText, Calendar, Sparkles, Activity } from "lucide-react";
import { ColorfulButton } from "@/components/ui/colorful-button";
import { ColorfulCard } from "@/components/ui/colorful-card";
import { ParticleBackground } from "@/components/ui/particle-background";
import { InteractiveTooltip } from "@/components/ui/interactive-tooltip";

export default function Records() {
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<MedicalRecord | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [recordTypeFilter, setRecordTypeFilter] = useState("all");
  const [animateItems, setAnimateItems] = useState(false);
  const [colorMode, setColorMode] = useState<'blue' | 'purple' | 'green' | 'rainbow'>('blue');
  const [activeTab, setActiveTab] = useState("all");

  useEffect(() => {
    // Animate items after a short delay for a staggered effect
    const timer = setTimeout(() => {
      setAnimateItems(true);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  const { data: records, isLoading } = useQuery({
    queryKey: ["/api/records"],
  });

  const filteredRecords = records
    ? records.filter((record: MedicalRecord) => {
        const matchesSearch =
          record.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
          (record.description && record.description.toLowerCase().includes(searchTerm.toLowerCase())) ||
          record.provider.toLowerCase().includes(searchTerm.toLowerCase());

        const matchesType = recordTypeFilter === "all" || record.recordType === recordTypeFilter;

        return matchesSearch && matchesType;
      })
    : [];

  const recordTypes = records
    ? Array.from(new Set(records.map((record: MedicalRecord) => record.recordType)))
    : [];

  return (
    <>
      <div className="min-h-screen relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:via-blue-950 dark:to-purple-950 py-6 px-4 sm:px-6 lg:px-8 transition-all duration-500">
        {/* Particle Background */}
        <ParticleBackground
          colorScheme={colorMode}
          particleCount={80}
          speed={0.3}
          interactive={true}
          density={0.8}
        />

        {/* Animated background glow effects */}
        <div className="fixed inset-0 -z-5 overflow-hidden opacity-70 dark:opacity-40">
          <div className="absolute top-1/4 -left-20 w-72 h-72 bg-blue-500/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-pulse-slow"></div>
          <div className="absolute bottom-1/4 -right-20 w-72 h-72 bg-purple-500/20 dark:bg-purple-500/10 rounded-full filter blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        </div>

        {/* Page Header */}
        <div className="mb-8 relative group">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-indigo-600/5 to-purple-600/5 dark:from-blue-400/10 dark:via-indigo-400/10 dark:to-purple-400/10 rounded-xl transform transition-all duration-300 group-hover:scale-105 group-hover:opacity-80"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-indigo-600/5 to-purple-600/5 dark:from-blue-400/10 dark:via-indigo-400/10 dark:to-purple-400/10 rounded-xl blur-xl opacity-50 transform transition-all duration-300 group-hover:opacity-100"></div>

          <div className="relative bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 dark:from-blue-500/20 dark:via-indigo-500/20 dark:to-purple-500/20 p-6 rounded-xl backdrop-blur-sm animate-fadeIn border border-white/10 dark:border-white/5 shadow-lg">
            <div className="flex flex-col sm:flex-row sm:items-center justify-between">
              <div className="flex items-start">
                <div className="relative mr-4">
                  <div className="absolute inset-0 bg-blue-500/30 dark:bg-blue-400/20 rounded-full filter blur-md animate-pulse-slow"></div>
                  <FileText className="h-10 w-10 text-blue-600 dark:text-blue-300 relative z-10 animate-float" />
                </div>

                <div className="flex-1">
                  <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-300 dark:to-purple-300 animate-gradient-shift">
                    Medical Records
                  </h1>
                  <div className="h-1 w-0 bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-400 dark:to-purple-400 mt-1 rounded-full animate-expand"></div>

                  <p className="mt-3 text-base text-gray-700 dark:text-gray-200">
                    View and manage all your medical documents in one secure place
                  </p>
                </div>
              </div>

              <div className="mt-4 sm:mt-0">
                <ColorfulButton
                  colorScheme="gradient-blue-purple"
                  glowEffect={true}
                  hoverAnimation="scale"
                  className="relative overflow-hidden group"
                  onClick={() => setIsUploadModalOpen(true)}
                >
                  <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-purple-600/20 dark:from-blue-400/20 dark:to-purple-400/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                  <span className="relative flex items-center">
                    <Plus className="mr-2 h-4 w-4" />
                    Upload Record
                  </span>
                </ColorfulButton>
              </div>
            </div>

            <div className="mt-4 flex flex-wrap gap-3">
              <div className="flex items-center space-x-2 bg-blue-500/10 dark:bg-blue-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.3s' }}>
                <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-pulse-slow"></div>
                <p className="text-xs font-medium text-blue-700 dark:text-blue-300">Secure Storage</p>
              </div>

              <div className="flex items-center space-x-2 bg-green-500/10 dark:bg-green-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.5s' }}>
                <div className="w-2 h-2 rounded-full bg-green-500 dark:bg-green-400 animate-pulse-slow"></div>
                <p className="text-xs font-medium text-green-700 dark:text-green-300">Easy Sharing</p>
              </div>

              <div className="flex items-center space-x-2 bg-purple-500/10 dark:bg-purple-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.7s' }}>
                <div className="w-2 h-2 rounded-full bg-purple-500 dark:bg-purple-400 animate-pulse-slow"></div>
                <p className="text-xs font-medium text-purple-700 dark:text-purple-300">Instant Access</p>
              </div>
            </div>
          </div>
        </div>

        <ColorfulCard
          className="mb-6 overflow-hidden animate-fadeIn"
          colorScheme="gradient-blue-purple"
          glowEffect={true}
          hoverAnimation="lift"
          entranceAnimation="fade-up"
        >
          <div className="p-4 bg-gradient-to-br from-blue-100/70 to-purple-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-grow group">
                <div className="absolute inset-0 bg-white/50 dark:bg-gray-700/50 rounded-md group-focus-within:bg-white dark:group-focus-within:bg-gray-700 transition-all duration-300"></div>
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 transition-all duration-300 group-focus-within:text-blue-500">
                  <Search className="h-4 w-4 text-gray-400 dark:text-gray-300 group-focus-within:text-blue-500 dark:group-focus-within:text-blue-400" />
                </div>
                <Input
                  placeholder="Search records..."
                  className="pl-10 border-gray-200 dark:border-gray-700 bg-transparent relative z-10 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-300"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
                <div className="absolute inset-0 border border-transparent group-focus-within:border-blue-500 dark:group-focus-within:border-blue-400 rounded-md pointer-events-none transition-all duration-300"></div>
              </div>

              <div className="flex items-center gap-3 relative group">
                <div className="relative">
                  <div className="absolute inset-0 bg-blue-400/10 dark:bg-blue-400/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <Filter className="h-5 w-5 text-blue-600 dark:text-blue-400 relative z-10" />
                </div>

                <div className="relative">
                  <select
                    className="appearance-none bg-white/50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-700 rounded-md px-4 py-2 pr-8 text-sm text-gray-700 dark:text-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 dark:focus:ring-blue-400 transition-all duration-300"
                    value={recordTypeFilter}
                    onChange={(e) => setRecordTypeFilter(e.target.value)}
                  >
                    <option value="all">All Types</option>
                    {recordTypes.map((type) => (
                      <option key={type} value={type}>
                        {type.charAt(0).toUpperCase() + type.slice(1).replace('_', ' ')}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg className="h-4 w-4 text-gray-400 dark:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </div>
                </div>

                <InteractiveTooltip content="Filter your records by type" side="top">
                  <div className="hidden sm:flex items-center justify-center h-6 w-6 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 cursor-help">
                    <span className="text-xs">?</span>
                  </div>
                </InteractiveTooltip>
              </div>
            </div>

            <div className="mt-3 flex flex-wrap gap-2">
              <div className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                <Sparkles className="h-3 w-3 mr-1 text-blue-500 dark:text-blue-400" />
                <span>Quick tip: Use keywords like "lab" or "prescription" to find specific records</span>
              </div>
            </div>
          </div>
        </ColorfulCard>

        <ColorfulCard
          className="overflow-hidden animate-fadeIn"
          colorScheme="gradient-blue-purple"
          glowEffect={true}
          entranceAnimation="fade-up"
        >
          <div className="p-4 bg-gradient-to-br from-blue-100/70 to-purple-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm">
            <Tabs
              defaultValue="all"
              className="w-full"
              onValueChange={(value) => setActiveTab(value)}
            >
              <div className="relative">
                <div className="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-blue-200 via-purple-200 to-blue-200 dark:from-blue-800 dark:via-purple-800 dark:to-blue-800"></div>
                <TabsList className="mb-4 bg-transparent relative z-10 space-x-2">
                  {[
                    { value: "all", label: "All Records", icon: <FileText className="h-4 w-4 mr-1" /> },
                    { value: "prescriptions", label: "Prescriptions", icon: <FileText className="h-4 w-4 mr-1" /> },
                    { value: "lab_reports", label: "Lab Reports", icon: <Activity className="h-4 w-4 mr-1" /> },
                    { value: "diagnostics", label: "Diagnostics", icon: <FileText className="h-4 w-4 mr-1" /> },
                    { value: "summaries", label: "Summaries", icon: <Calendar className="h-4 w-4 mr-1" /> }
                  ].map((tab) => (
                    <TabsTrigger
                      key={tab.value}
                      value={tab.value}
                      className={`
                        relative overflow-hidden transition-all duration-300
                        data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500/10 data-[state=active]:to-purple-500/10
                        data-[state=active]:dark:from-blue-500/20 data-[state=active]:dark:to-purple-500/20
                        data-[state=active]:text-blue-700 data-[state=active]:dark:text-blue-300
                        data-[state=active]:border-b-2 data-[state=active]:border-blue-500 data-[state=active]:dark:border-blue-400
                        data-[state=active]:shadow-sm
                      `}
                    >
                      <div className="flex items-center">
                        {tab.icon}
                        {tab.label}
                      </div>
                      {activeTab === tab.value && (
                        <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 dark:from-blue-400 dark:to-purple-400"></div>
                      )}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </div>

              <div className={`transition-all duration-300 ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
                <TabsContent value="all" className="w-full mt-4">
                  {isLoading ? (
                    <div className="space-y-4">
                      {[...Array(3)].map((_, i) => (
                        <div key={i} className="relative overflow-hidden rounded-lg animate-pulse-slow" style={{ animationDelay: `${i * 0.1}s` }}>
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10 rounded-lg"></div>
                          <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm p-4 backdrop-blur-sm">
                            <Skeleton className="h-6 w-48 mb-2 bg-blue-200/50 dark:bg-blue-700/50 rounded-lg" />
                            <Skeleton className="h-4 w-64 mb-1 bg-blue-100/50 dark:bg-blue-800/50 rounded-lg" />
                            <Skeleton className="h-4 w-32 bg-blue-100/30 dark:bg-blue-800/30 rounded-lg" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <RecordList
                      records={filteredRecords}
                      onRecordSelect={(record) => setSelectedRecord(record)}
                    />
                  )}
                </TabsContent>

                <TabsContent value="prescriptions" className="mt-4">
                  {isLoading ? (
                    <div className="space-y-4">
                      {[...Array(2)].map((_, i) => (
                        <div key={i} className="relative overflow-hidden rounded-lg animate-pulse-slow" style={{ animationDelay: `${i * 0.1}s` }}>
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10 rounded-lg"></div>
                          <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm p-4 backdrop-blur-sm">
                            <Skeleton className="h-6 w-48 mb-2 bg-blue-200/50 dark:bg-blue-700/50 rounded-lg" />
                            <Skeleton className="h-4 w-64 mb-1 bg-blue-100/50 dark:bg-blue-800/50 rounded-lg" />
                            <Skeleton className="h-4 w-32 bg-blue-100/30 dark:bg-blue-800/30 rounded-lg" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <RecordList
                      records={filteredRecords.filter((r: MedicalRecord) => r.recordType === 'prescription')}
                      onRecordSelect={(record) => setSelectedRecord(record)}
                    />
                  )}
                </TabsContent>

                <TabsContent value="lab_reports" className="mt-4">
                  {isLoading ? (
                    <div className="space-y-4">
                      {[...Array(2)].map((_, i) => (
                        <div key={i} className="relative overflow-hidden rounded-lg animate-pulse-slow" style={{ animationDelay: `${i * 0.1}s` }}>
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10 rounded-lg"></div>
                          <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm p-4 backdrop-blur-sm">
                            <Skeleton className="h-6 w-48 mb-2 bg-blue-200/50 dark:bg-blue-700/50 rounded-lg" />
                            <Skeleton className="h-4 w-64 mb-1 bg-blue-100/50 dark:bg-blue-800/50 rounded-lg" />
                            <Skeleton className="h-4 w-32 bg-blue-100/30 dark:bg-blue-800/30 rounded-lg" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <RecordList
                      records={filteredRecords.filter((r: MedicalRecord) => r.recordType === 'lab_report')}
                      onRecordSelect={(record) => setSelectedRecord(record)}
                    />
                  )}
                </TabsContent>

                <TabsContent value="diagnostics" className="mt-4">
                  {isLoading ? (
                    <div className="space-y-4">
                      {[...Array(2)].map((_, i) => (
                        <div key={i} className="relative overflow-hidden rounded-lg animate-pulse-slow" style={{ animationDelay: `${i * 0.1}s` }}>
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10 rounded-lg"></div>
                          <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm p-4 backdrop-blur-sm">
                            <Skeleton className="h-6 w-48 mb-2 bg-blue-200/50 dark:bg-blue-700/50 rounded-lg" />
                            <Skeleton className="h-4 w-64 mb-1 bg-blue-100/50 dark:bg-blue-800/50 rounded-lg" />
                            <Skeleton className="h-4 w-32 bg-blue-100/30 dark:bg-blue-800/30 rounded-lg" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <RecordList
                      records={filteredRecords.filter((r: MedicalRecord) => r.recordType === 'diagnostic_image')}
                      onRecordSelect={(record) => setSelectedRecord(record)}
                    />
                  )}
                </TabsContent>

                <TabsContent value="summaries" className="mt-4">
                  {isLoading ? (
                    <div className="space-y-4">
                      {[...Array(2)].map((_, i) => (
                        <div key={i} className="relative overflow-hidden rounded-lg animate-pulse-slow" style={{ animationDelay: `${i * 0.1}s` }}>
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10 rounded-lg"></div>
                          <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm p-4 backdrop-blur-sm">
                            <Skeleton className="h-6 w-48 mb-2 bg-blue-200/50 dark:bg-blue-700/50 rounded-lg" />
                            <Skeleton className="h-4 w-64 mb-1 bg-blue-100/50 dark:bg-blue-800/50 rounded-lg" />
                            <Skeleton className="h-4 w-32 bg-blue-100/30 dark:bg-blue-800/30 rounded-lg" />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <RecordList
                      records={filteredRecords.filter((r: MedicalRecord) => r.recordType === 'summary')}
                      onRecordSelect={(record) => setSelectedRecord(record)}
                    />
                  )}
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </ColorfulCard>
      </div>

      {/* Enhanced Modal Components */}
      <RecordUploadModal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
      />

      {selectedRecord && (
        <RecordViewer
          record={selectedRecord}
          isOpen={!!selectedRecord}
          onClose={() => setSelectedRecord(null)}
        />
      )}
    </>
  );
}

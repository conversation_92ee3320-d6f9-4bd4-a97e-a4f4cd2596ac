import React, { useState } from "react";
import { Button, ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";

type ColorScheme = 
  | "blue" 
  | "purple" 
  | "green" 
  | "pink" 
  | "orange" 
  | "red" 
  | "gradient-blue-purple" 
  | "gradient-green-blue" 
  | "gradient-orange-red" 
  | "gradient-pink-purple"
  | "gradient-cyan-blue"
  | "gradient-yellow-orange";

interface ColorfulButtonProps extends ButtonProps {
  colorScheme?: ColorScheme;
  glowEffect?: boolean;
  pulseEffect?: boolean;
  hoverAnimation?: "scale" | "rotate" | "lift" | "none";
  children: React.ReactNode;
}

export function ColorfulButton({
  colorScheme = "blue",
  glowEffect = false,
  pulseEffect = false,
  hoverAnimation = "scale",
  children,
  className,
  ...props
}: ColorfulButtonProps) {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);

  const getColorClasses = () => {
    switch (colorScheme) {
      case "blue":
        return "bg-blue-500 hover:bg-blue-600 text-white";
      case "purple":
        return "bg-purple-500 hover:bg-purple-600 text-white";
      case "green":
        return "bg-green-500 hover:bg-green-600 text-white";
      case "pink":
        return "bg-pink-500 hover:bg-pink-600 text-white";
      case "orange":
        return "bg-orange-500 hover:bg-orange-600 text-white";
      case "red":
        return "bg-red-500 hover:bg-red-600 text-white";
      case "gradient-blue-purple":
        return "bg-gradient-blue-purple text-white";
      case "gradient-green-blue":
        return "bg-gradient-green-blue text-white";
      case "gradient-orange-red":
        return "bg-gradient-orange-red text-white";
      case "gradient-pink-purple":
        return "bg-gradient-pink-purple text-white";
      case "gradient-cyan-blue":
        return "bg-gradient-cyan-blue text-white";
      case "gradient-yellow-orange":
        return "bg-gradient-yellow-orange text-white";
      default:
        return "bg-blue-500 hover:bg-blue-600 text-white";
    }
  };

  const getHoverClass = () => {
    switch (hoverAnimation) {
      case "scale":
        return "hover-scale";
      case "rotate":
        return "hover-rotate";
      case "lift":
        return "hover-lift";
      case "none":
        return "";
      default:
        return "hover-scale";
    }
  };

  const getShadowClass = () => {
    if (!glowEffect) return "";
    
    switch (colorScheme) {
      case "blue":
      case "gradient-cyan-blue":
      case "gradient-green-blue":
        return "shadow-blue";
      case "purple":
      case "gradient-blue-purple":
      case "gradient-pink-purple":
        return "shadow-purple";
      case "green":
        return "shadow-green";
      case "pink":
        return "shadow-pink";
      case "orange":
      case "gradient-yellow-orange":
        return "shadow-orange";
      case "red":
      case "gradient-orange-red":
        return "shadow-red";
      default:
        return "shadow-blue";
    }
  };

  return (
    <Button
      className={cn(
        "relative overflow-hidden transition-all duration-300",
        getColorClasses(),
        getHoverClass(),
        getShadowClass(),
        pulseEffect && "animate-pulse-glow",
        isPressed && "scale-95 opacity-90",
        colorScheme.startsWith("gradient") && "animate-gradient-shift",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      {...props}
    >
      {isHovered && (
        <span className="absolute inset-0 bg-white opacity-10 animate-shimmer pointer-events-none" />
      )}
      {children}
    </Button>
  );
}

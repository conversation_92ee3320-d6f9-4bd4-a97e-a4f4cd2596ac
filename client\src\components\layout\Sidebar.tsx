import { Link, useLocation } from "wouter";
import { cn } from "@/lib/utils";
import { MediKeyLogo } from "@/assets/icons/MediKeyLogo";
import { useAuth } from "@/hooks/useAuth";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { ThemeCustomizer } from "@/components/theme/theme-customizer";
import { ColorfulButton } from "@/components/ui/colorful-button";
import { InteractiveTooltip } from "@/components/ui/interactive-tooltip";
import { useState, useEffect } from "react";
import {
  LayoutDashboard,
  FolderClosed,
  BarChart2,
  Users,
  Calendar,
  Bot,
  AlertTriangle,
  Settings,
  LogOut,
  Smartphone,
  Sparkles,
  ChevronRight,
  Bell,
  Heart,
  Shield,
  Activity
} from "lucide-react";

export default function Sidebar() {
  const [location] = useLocation();
  const { user, logout } = useAuth();
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const [expandedSection, setExpandedSection] = useState<string | null>(null);
  const [animateItems, setAnimateItems] = useState(false);
  const [showNotification, setShowNotification] = useState(true);

  // Get the base path for GitHub Pages
  const isGitHubPages = window.location.hostname.includes('github.io');
  const basePath = isGitHubPages ? '/medikey' : '';

  // Animate items after component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimateItems(true);
    }, 100);

    // Auto-hide notification after 10 seconds
    const notificationTimer = setTimeout(() => {
      setShowNotification(false);
    }, 10000);

    return () => {
      clearTimeout(timer);
      clearTimeout(notificationTimer);
    };
  }, []);

  const navItems = [
    {
      href: `${basePath}/dashboard`,
      icon: <LayoutDashboard size={18} />,
      activeIcon: <LayoutDashboard size={18} />,
      label: "Dashboard",
      description: "Your health overview and quick access",
      color: "blue",
      notification: null
    },
    {
      href: `${basePath}/records`,
      icon: <FolderClosed size={18} />,
      activeIcon: <FolderClosed size={18} />,
      label: "My Records",
      description: "View and manage your medical records",
      color: "indigo",
      notification: null
    },
    {
      href: `${basePath}/analytics`,
      icon: <BarChart2 size={18} />,
      activeIcon: <Activity size={18} />,
      label: "Health Analytics",
      description: "Track and visualize your health metrics",
      color: "purple",
      notification: showNotification ? "3 new insights available" : null
    },
    {
      href: `${basePath}/family`,
      icon: <Users size={18} />,
      activeIcon: <Users size={18} />,
      label: "Family Vault",
      description: "Manage your family's health records",
      color: "cyan",
      notification: null
    },
    {
      href: `${basePath}/appointments`,
      icon: <Calendar size={18} />,
      activeIcon: <Calendar size={18} />,
      label: "Appointments",
      description: "Schedule and manage appointments",
      color: "green",
      notification: showNotification ? "Upcoming: Dr. Smith (Tomorrow)" : null
    },
    {
      href: `${basePath}/assistant`,
      icon: <Bot size={18} />,
      activeIcon: <Bot size={18} />,
      label: "AI Assistant",
      description: "Get AI-powered health insights and answers",
      color: "amber",
      notification: null
    },
  ];

  // Helper function to get color classes based on color name
  const getColorClasses = (color: string, isActive: boolean = false) => {
    const baseClasses = isActive ? "text-white" : "text-gray-600 dark:text-gray-300";

    switch (color) {
      case "blue":
        return isActive
          ? "bg-gradient-to-r from-blue-600 to-blue-500 text-white"
          : "hover:bg-blue-50 hover:text-blue-600 dark:hover:bg-blue-900/20 dark:hover:text-blue-400";
      case "indigo":
        return isActive
          ? "bg-gradient-to-r from-indigo-600 to-indigo-500 text-white"
          : "hover:bg-indigo-50 hover:text-indigo-600 dark:hover:bg-indigo-900/20 dark:hover:text-indigo-400";
      case "purple":
        return isActive
          ? "bg-gradient-to-r from-purple-600 to-purple-500 text-white"
          : "hover:bg-purple-50 hover:text-purple-600 dark:hover:bg-purple-900/20 dark:hover:text-purple-400";
      case "cyan":
        return isActive
          ? "bg-gradient-to-r from-cyan-600 to-cyan-500 text-white"
          : "hover:bg-cyan-50 hover:text-cyan-600 dark:hover:bg-cyan-900/20 dark:hover:text-cyan-400";
      case "green":
        return isActive
          ? "bg-gradient-to-r from-green-600 to-green-500 text-white"
          : "hover:bg-green-50 hover:text-green-600 dark:hover:bg-green-900/20 dark:hover:text-green-400";
      case "amber":
        return isActive
          ? "bg-gradient-to-r from-amber-600 to-amber-500 text-white"
          : "hover:bg-amber-50 hover:text-amber-600 dark:hover:bg-amber-900/20 dark:hover:text-amber-400";
      default:
        return isActive
          ? "bg-gradient-to-r from-primary-600 to-primary-500 text-white"
          : "hover:bg-primary-50 hover:text-primary-600 dark:hover:bg-primary-900/20 dark:hover:text-primary-400";
    }
  };

  return (
    <aside className="hidden md:flex md:flex-col w-64 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-950 border-r border-gray-200 dark:border-gray-800 shadow-sm transition-all duration-300">
      {/* Logo */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10"></div>
        <div className="flex items-center justify-center h-16 border-b border-gray-200 dark:border-gray-800 relative z-10">
          <div className="flex items-center group">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-500/10 dark:bg-blue-400/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <MediKeyLogo className="h-8 w-8 text-primary-600 dark:text-primary-400 animate-pulse-slow relative z-10" />
            </div>
            <div className="ml-2 relative">
              <span className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">MediKey</span>
              <div className="h-0.5 w-0 bg-gradient-to-r from-blue-500 to-purple-500 dark:from-blue-400 dark:to-purple-400 group-hover:w-full transition-all duration-300"></div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-3 py-4 overflow-y-auto">
        <div className="space-y-1.5">
          {navItems.map((item, index) => {
            const isActive = location === item.href;
            const isHovered = hoveredItem === item.href;
            const colorClasses = getColorClasses(item.color, isActive);

            return (
              <div
                key={item.href}
                className={`transition-all duration-300 ${animateItems ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4'}`}
                style={{ transitionDelay: `${index * 50}ms` }}
              >
                <Link
                  href={item.href}
                  onMouseEnter={() => setHoveredItem(item.href)}
                  onMouseLeave={() => setHoveredItem(null)}
                  onClick={() => setExpandedSection(expandedSection === item.href ? null : item.href)}
                  className={cn(
                    "group relative flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-300",
                    colorClasses,
                    isActive && "shadow-md"
                  )}
                >
                  {/* Background glow effect on hover */}
                  {isHovered && !isActive && (
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10 rounded-lg -z-10"></div>
                  )}

                  {/* Icon container with animation */}
                  <div className={`relative mr-3 transition-all duration-300 ${isActive ? 'scale-110' : 'group-hover:scale-110'}`}>
                    <div className={`absolute inset-0 rounded-full ${isActive ? 'bg-white/20' : 'bg-current opacity-0 group-hover:opacity-10'} transition-opacity duration-300`}></div>
                    <span className={cn(
                      isActive ? "text-white" : "text-gray-500 dark:text-gray-400 group-hover:text-current"
                    )}>
                      {isActive ? item.activeIcon : item.icon}
                    </span>
                  </div>

                  {/* Label */}
                  <span className="flex-1">{item.label}</span>

                  {/* Notification indicator */}
                  {item.notification && (
                    <div className="ml-2 flex items-center">
                      <span className="relative flex h-2 w-2">
                        <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                        <span className="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
                      </span>
                    </div>
                  )}

                  {/* Expand/collapse indicator */}
                  {item.notification && (
                    <ChevronRight
                      size={16}
                      className={`ml-2 transition-transform duration-300 ${expandedSection === item.href ? 'rotate-90' : ''}`}
                    />
                  )}
                </Link>

                {/* Expanded notification content */}
                {item.notification && expandedSection === item.href && (
                  <div className="mt-1 ml-9 pl-3 border-l-2 border-dashed border-gray-300 dark:border-gray-700 text-xs text-gray-600 dark:text-gray-300 py-2 animate-fadeIn">
                    {item.notification}
                  </div>
                )}
              </div>
            );
          })}
        </div>

        <div className="mt-8">
          <div className="flex items-center px-3 mb-2">
            <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider flex-1">
              Emergency
            </h3>
            <div className="h-px flex-1 bg-gradient-to-r from-red-200 to-transparent dark:from-red-800 ml-2"></div>
          </div>

          <div className={`transition-all duration-300 ${animateItems ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4'}`} style={{ transitionDelay: '350ms' }}>
            <div className="relative p-0.5 rounded-lg overflow-hidden">
              {/* Animated border effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-orange-500 dark:from-red-600 dark:to-orange-600 animate-pulse-slow rounded-lg"></div>

              <div className="relative bg-red-50 dark:bg-red-900/20 rounded-md p-3 backdrop-blur-sm">
                <Link
                  href={`${basePath}/emergency`}
                  className="flex items-center text-sm font-medium text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300 group"
                >
                  <div className="relative mr-3">
                    <div className="absolute inset-0 bg-red-400/20 dark:bg-red-400/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <Shield className="h-5 w-5 text-red-600 dark:text-red-400 relative z-10 transition-transform duration-300 group-hover:scale-110" />
                  </div>
                  <span className="relative">
                    Emergency Mode
                    <div className="h-0.5 w-0 bg-red-500 dark:bg-red-400 group-hover:w-full transition-all duration-300"></div>
                  </span>
                  <div className="ml-auto">
                    <span className="relative flex h-2 w-2">
                      <span className="animate-ping absolute inline-flex h-full w-full rounded-full bg-red-400 opacity-75"></span>
                      <span className="relative inline-flex rounded-full h-2 w-2 bg-red-500"></span>
                    </span>
                  </div>
                </Link>
                <p className="mt-2 text-xs text-red-500 dark:text-red-400 pl-8">Instant access to vital information for emergency personnel</p>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6">
          <div className="flex items-center px-3 mb-2">
            <h3 className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider flex-1">
              Mobile Access
            </h3>
            <div className="h-px flex-1 bg-gradient-to-r from-blue-200 to-transparent dark:from-blue-800 ml-2"></div>
          </div>

          <div className={`transition-all duration-300 ${animateItems ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4'}`} style={{ transitionDelay: '400ms' }}>
            <div className="relative p-0.5 rounded-lg overflow-hidden">
              {/* Animated border effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-blue-600 dark:to-indigo-600 animate-pulse-slow rounded-lg"></div>

              <div className="relative bg-blue-50 dark:bg-blue-900/20 rounded-md p-3 backdrop-blur-sm">
                <Link
                  href={`${basePath}/mobile-access`}
                  className="flex items-center text-sm font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 group"
                >
                  <div className="relative mr-3">
                    <div className="absolute inset-0 bg-blue-400/20 dark:bg-blue-400/30 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <Smartphone className="h-5 w-5 text-blue-600 dark:text-blue-400 relative z-10 transition-transform duration-300 group-hover:scale-110" />
                  </div>
                  <span className="relative">
                    Access on Phone
                    <div className="h-0.5 w-0 bg-blue-500 dark:bg-blue-400 group-hover:w-full transition-all duration-300"></div>
                  </span>
                </Link>
                <p className="mt-2 text-xs text-blue-500 dark:text-blue-400 pl-8">Scan QR code to use MediKey on your phone</p>

                {/* New feature hint */}
                <div className="mt-2 flex items-center pl-8">
                  <div className="flex items-center space-x-1 bg-blue-100 dark:bg-blue-800/30 px-2 py-0.5 rounded text-xs text-blue-600 dark:text-blue-300">
                    <Sparkles className="h-3 w-3" />
                    <span>New! Smartwatch support added</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </nav>

      {/* Theme Controls */}
      <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-800 bg-gradient-to-r from-gray-50 to-white dark:from-gray-900 dark:to-gray-950">
        <div className={`flex items-center justify-between transition-all duration-300 ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '450ms' }}>
          <div className="flex items-center">
            <div className="relative mr-2">
              <div className="absolute inset-0 bg-purple-400/10 dark:bg-purple-400/20 rounded-full blur-sm animate-pulse-slow"></div>
              <Sparkles className="h-4 w-4 text-purple-500 dark:text-purple-400 relative z-10" />
            </div>
            <p className="text-xs font-semibold text-gray-700 dark:text-gray-300">Appearance</p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-400/10 dark:bg-blue-400/20 rounded-full blur-sm opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
              <ThemeToggle />
            </div>
            <div className="relative">
              <div className="absolute inset-0 bg-purple-400/10 dark:bg-purple-400/20 rounded-full blur-sm opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
              <ThemeCustomizer />
            </div>
          </div>
        </div>
      </div>

      {/* User Profile */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 dark:from-blue-500/10 dark:to-purple-500/10"></div>

        <div className={`flex items-center p-4 border-t border-gray-200 dark:border-gray-800 relative z-10 transition-all duration-300 ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '500ms' }}>
          <div className="relative group">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 dark:from-blue-400/20 dark:to-purple-400/20 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <Avatar className="border-2 border-white dark:border-gray-800 shadow-md transition-transform duration-300 group-hover:scale-110">
              <AvatarImage src={user?.avatarUrl || ""} alt={user?.username} />
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-500 dark:from-blue-400 dark:to-purple-400 text-white">
                {user?.username.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
          </div>

          <div className="ml-3 flex-1 overflow-hidden">
            <p className="text-sm font-medium text-gray-800 dark:text-gray-200 truncate group">
              <span className="relative">
                {user?.username}
                <div className="h-0.5 w-0 bg-gradient-to-r from-blue-500 to-purple-500 dark:from-blue-400 dark:to-purple-400 group-hover:w-full transition-all duration-300"></div>
              </span>
            </p>

            <ColorfulButton
              colorScheme="gradient-blue-purple"
              variant="ghost"
              size="sm"
              className="h-8 px-2 text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-100 mt-1 group"
              onClick={logout}
            >
              <LogOut className="h-3 w-3 mr-1.5 transition-transform duration-300 group-hover:-translate-x-0.5" />
              Sign out
            </ColorfulButton>
          </div>

          <InteractiveTooltip content="Settings" side="left">
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-400/10 dark:to-purple-400/10 rounded-full blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <Link href={`${basePath}/profile`}>
                <Button variant="ghost" size="sm" className="h-9 w-9 p-0 rounded-full relative z-10 transition-transform duration-300 group-hover:rotate-12">
                  <Settings className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                </Button>
              </Link>
            </div>
          </InteractiveTooltip>
        </div>
      </div>
    </aside>
  );
}

import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { MediKeyLogo } from "@/assets/icons/MediKeyLogo";
import { AlertCircle, Loader2, Shield, Heart, FileText, Users, Sparkles } from "lucide-react";
import { AnimatedButton } from "@/components/ui/animated-button";
import { AnimatedCard } from "@/components/ui/animated-card";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { ColorfulButton } from "@/components/ui/colorful-button";
import { ColorfulCard } from "@/components/ui/colorful-card";
import { ParticleBackground } from "@/components/ui/particle-background";
import { GoogleSignInButton } from "@/components/auth/GoogleSignInButton";
import { Separator } from "@/components/ui/separator";

const loginSchema = z.object({
  username: z.string().min(1, "Username is required"),
  password: z.string().min(1, "Password is required"),
});

type LoginFormData = z.infer<typeof loginSchema>;

export default function Login() {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const [error, setError] = useState<string | null>(null);
  const [animateIn, setAnimateIn] = useState(false);
  const [activeFeature, setActiveFeature] = useState<number | null>(null);
  const [basePath, setBasePath] = useState('');

  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setAnimateIn(true);
    }, 100);

    // Auto-rotate through features
    const featureInterval = setInterval(() => {
      setActiveFeature((prev) => {
        if (prev === null || prev >= 3) return 0;
        return prev + 1;
      });
    }, 3000);

    // Determine the base path for GitHub Pages
    const isGitHubPages = window.location.hostname.includes('github.io');
    setBasePath(isGitHubPages ? '/medikey' : '');

    return () => {
      clearTimeout(timer);
      clearInterval(featureInterval);
    };
  }, []);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: "",
      password: "",
    },
  });

  const loginMutation = useMutation({
    mutationFn: async (data: LoginFormData) => {
      try {
        // Import the loginUser function from the API module
        const { loginUser } = await import("@/lib/api");
        return loginUser(data.username, data.password);
      } catch (error) {
        console.error("Login error:", error);
        throw error;
      }
    },
    onSuccess: () => {
      toast({
        title: "Login successful",
        description: "Welcome to MediKey!",
      });
      // Use the basePath for navigation
      navigate(`${basePath}/dashboard`);

      // Add console logs for debugging
      console.log("Login successful, navigating to:", `${basePath}/dashboard`);
    },
    onError: (error: any) => {
      console.error("Login error:", error);
      setError("Invalid username or password. Please try again.");
      toast({
        title: "Login failed",
        description: error.message || "An unknown error occurred",
        variant: "destructive",
      });
    },
  });

  const onSubmit = (data: LoginFormData) => {
    setError(null);
    loginMutation.mutate(data);
  };

  // Features to highlight
  const features = [
    {
      icon: <Shield className="h-6 w-6 text-primary-600" />,
      title: "Secure Health Records",
      description: "Your medical data is encrypted and protected"
    },
    {
      icon: <Heart className="h-6 w-6 text-primary-600" />,
      title: "Health Analytics",
      description: "Track and visualize your health metrics"
    },
    {
      icon: <FileText className="h-6 w-6 text-primary-600" />,
      title: "Digital Records",
      description: "Access your medical history anytime, anywhere"
    },
    {
      icon: <Users className="h-6 w-6 text-primary-600" />,
      title: "Family Management",
      description: "Manage health records for your entire family"
    }
  ];

  return (
    <div className="min-h-screen flex flex-col md:flex-row items-stretch relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-950">
      {/* Particle Background */}
      <ParticleBackground
        colorScheme="rainbow"
        particleCount={80}
        speed={0.5}
        interactive={true}
        density={1.2}
      />

      {/* Left side - Login Form */}
      <div className={`w-full md:w-1/2 flex items-center justify-center p-8 transition-all duration-700 ${animateIn ? 'opacity-100' : 'opacity-0'} relative z-10`}>
        <div className="w-full max-w-md">
          <div className="flex justify-end mb-4">
            <ThemeToggle />
          </div>

          <div className="text-center mb-8 animate-fadeIn bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 p-4 rounded-xl backdrop-blur-sm">
            <div className="flex items-center justify-center">
              <MediKeyLogo className="h-12 w-12 text-primary-600 dark:text-primary-300 animate-pulse-slow" />
              <h1 className="ml-3 text-3xl font-bold text-gradient text-blue-gradient drop-shadow-lg">MediKey</h1>
            </div>
            <p className="mt-2 text-gray-700 dark:text-gray-100">Universal Digital Medical Record System</p>
          </div>

          <ColorfulCard
            className="shadow-lg transition-all duration-500 dark:bg-gray-900/90 backdrop-blur-sm"
            colorScheme="gradient-blue-purple"
            glowEffect={true}
            borderGradient={true}
            hoverAnimation="lift"
            entranceAnimation="scale"
          >
            <CardHeader className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 dark:from-blue-900/40 dark:to-purple-900/40 backdrop-blur-sm">
              <CardTitle className="text-center text-white drop-shadow-md">
                <Sparkles className="inline-block h-5 w-5 mr-2 animate-pulse-glow text-yellow-300" />
                Sign in to your account
              </CardTitle>
              <CardDescription className="text-center text-white/90 dark:text-gray-100">
                Access your medical records securely
              </CardDescription>
            </CardHeader>
            <CardContent className="bg-gradient-to-br from-gray-100/80 to-white/60 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm">
              {error && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
              <form onSubmit={handleSubmit(onSubmit)}>
                <div className="space-y-4">
                  <div className="space-y-2 animate-fadeIn stagger-1">
                    <Label htmlFor="username" className="text-gray-800 dark:text-gray-100 font-medium">Username</Label>
                    <Input
                      id="username"
                      type="text"
                      placeholder="Enter your username"
                      className="transition-all duration-300 focus:ring-2 focus:ring-primary-500 bg-white/70 dark:bg-gray-700/70 border-blue-200 dark:border-blue-900 dark:text-white"
                      {...register("username")}
                    />
                    {errors.username && (
                      <p className="text-sm text-red-500 dark:text-red-300">{errors.username.message}</p>
                    )}
                  </div>
                  <div className="space-y-2 animate-fadeIn stagger-2">
                    <div className="flex items-center justify-between">
                      <Label htmlFor="password" className="text-gray-800 dark:text-gray-100 font-medium">Password</Label>
                      <AnimatedButton variant="link" className="p-0 h-auto text-xs text-blue-600 dark:text-blue-300">
                        Forgot password?
                      </AnimatedButton>
                    </div>
                    <Input
                      id="password"
                      type="password"
                      placeholder="Enter your password"
                      className="transition-all duration-300 focus:ring-2 focus:ring-primary-500 bg-white/70 dark:bg-gray-700/70 border-blue-200 dark:border-blue-900 dark:text-white"
                      {...register("password")}
                    />
                    {errors.password && (
                      <p className="text-sm text-red-500 dark:text-red-300">{errors.password.message}</p>
                    )}
                  </div>
                  <div className="animate-fadeIn stagger-3">
                    <ColorfulButton
                      type="submit"
                      className="w-full"
                      disabled={loginMutation.isPending}
                      colorScheme="gradient-blue-purple"
                      glowEffect={true}
                      pulseEffect={true}
                      hoverAnimation="scale"
                    >
                      {loginMutation.isPending ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Signing in...
                        </>
                      ) : (
                        "Sign in"
                      )}
                    </ColorfulButton>

                    <div className="mt-4 relative">
                      <div className="absolute inset-0 flex items-center">
                        <Separator className="w-full" />
                      </div>
                      <div className="relative flex justify-center text-xs uppercase">
                        <span className="bg-background px-2 text-muted-foreground dark:bg-gray-800">
                          Or continue with
                        </span>
                      </div>
                    </div>

                    <div className="mt-4">
                      <GoogleSignInButton />
                    </div>
                  </div>
                </div>
              </form>
            </CardContent>
            <CardFooter className="flex justify-center bg-gradient-to-r from-blue-500/20 to-purple-500/20 dark:from-blue-900/40 dark:to-purple-900/40 backdrop-blur-sm">
              <p className="text-sm text-gray-800 dark:text-gray-100">
                Don't have an account?{" "}
                <ColorfulButton
                  variant="link"
                  className="p-0 h-auto text-pink-600 dark:text-pink-300"
                  colorScheme="gradient-pink-purple"
                  onClick={() => navigate(`${basePath}/register`)}
                >
                  Sign up
                </ColorfulButton>
              </p>
            </CardFooter>
          </ColorfulCard>

          <div className="mt-6 text-center animate-fadeIn stagger-4">
            <p className="text-xs text-gray-700 dark:text-gray-200 backdrop-blur-sm bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 p-3 rounded-md">
              By signing in, you agree to our{" "}
              <a href="#" className="text-gradient text-blue-gradient font-medium">
                Terms of Service
              </a>{" "}
              and{" "}
              <a href="#" className="text-gradient text-pink-gradient font-medium">
                Privacy Policy
              </a>
            </p>
          </div>
        </div>
      </div>

      {/* Right side - Features */}
      <div className="hidden md:flex md:w-1/2 bg-gradient-to-br from-blue-600 to-purple-700 dark:from-blue-800 dark:to-purple-900 text-white items-center justify-center relative z-10">
        <div className="absolute inset-0 bg-gradient-to-t from-blue-900/40 to-transparent backdrop-blur-sm animate-gradient-shift"></div>
        <div className="p-12 max-w-md relative z-10">
          <h2 className="text-3xl font-bold mb-8 animate-fadeIn drop-shadow-lg">
            <span className="text-gradient text-pink-gradient">Your Health,</span> <span className="text-white">Digitized</span>
          </h2>

          <div className="space-y-6">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`flex items-start transition-all duration-500 animate-fadeIn ${
                  activeFeature === index
                    ? 'transform scale-105 opacity-100 shadow-lg shadow-purple-500/30 bg-gradient-to-r from-blue-500/20 to-purple-500/20 p-4 rounded-lg border border-white/20'
                    : 'opacity-80 p-2'
                }`}
                style={{ animationDelay: `${index * 200}ms` }}
                onMouseEnter={() => setActiveFeature(index)}
              >
                <div className={`flex-shrink-0 bg-gradient-to-br from-blue-400/30 to-purple-400/30 p-3 rounded-lg mr-4 ${
                  activeFeature === index ? 'animate-pulse-slow shadow-md shadow-purple-500/30' : ''
                }`}>
                  {feature.icon}
                </div>
                <div>
                  <h3 className="font-medium text-lg text-white">{feature.title}</h3>
                  <p className="text-white/90 mt-1">{feature.description}</p>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-12 bg-gradient-to-br from-blue-400/20 to-purple-400/20 backdrop-blur-sm rounded-lg p-6 border border-white/20 animate-float shadow-lg shadow-purple-500/20">
            <p className="italic text-white">
              "MediKey has transformed how I manage my family's health records. Everything is now in one secure place."
            </p>
            <p className="mt-4 font-medium text-white">— Sarah Johnson, <span className="text-gradient text-pink-gradient">MediKey User</span></p>
          </div>
        </div>
      </div>
    </div>
  );
}

import { createRoot } from "react-dom/client";
import React from "react";
import App from "./App";
import "./index.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "./lib/queryClient";
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from "@/hooks/useAuth";
import { Router } from "wouter";

// Custom hook to get the base path for GitHub Pages
const useBasePath = () => {
  const isGitHubPages = window.location.hostname.includes('github.io');
  return isGitHubPages ? '/medikey' : '';
};

// Create a custom router for GitHub Pages
const GitHubPagesRouter = ({ children }: { children: React.ReactNode }) => {
  const base = useBasePath();

  // Log the base path for debugging
  console.log('Base path in router:', base);

  return (
    <Router base={base}>
      {children}
    </Router>
  );
};

createRoot(document.getElementById("root")!).render(
  <QueryClientProvider client={queryClient}>
    <GitHubPagesRouter>
      <AuthProvider>
        <App />
        <Toaster />
      </AuthProvider>
    </GitHubPagesRouter>
  </QueryClientProvider>
);

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { updateUserProfile, updateEmergencyInfo } from "@/lib/api";
import { User } from "@shared/schema";
import {
  Loader2, Save, Shield, User as UserIcon, Settings,
  Calendar, Phone, Mail, Heart, AlertCircle,
  UserCheck, Clock, CheckCircle, Activity
} from "lucide-react";
import { useLocation } from "wouter";
import { AvatarUpload } from "@/components/profile/AvatarUpload";

const profileFormSchema = z.object({
  fullName: z.string().min(1, "Full name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  gender: z.string().optional(),
});

const emergencyInfoFormSchema = z.object({
  bloodType: z.string().optional(),
  allergies: z.string().optional(),
  chronicConditions: z.string().optional(),
  emergencyContactName: z.string().optional(),
  emergencyContactPhone: z.string().optional(),
});

export default function Profile() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [activeTab, setActiveTab] = useState("personal");
  const [location] = useLocation();

  // Check URL parameters for tab selection
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const tab = params.get("tab");
    if (tab === "emergency" || tab === "personal") {
      setActiveTab(tab);
    }
  }, [location]);

  const { data: userProfile, isLoading } = useQuery<User>({
    queryKey: ["/api/users/profile"],
  });

  const profileForm = useForm<z.infer<typeof profileFormSchema>>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      dateOfBirth: "",
      gender: "",
    },
  });

  const emergencyForm = useForm<z.infer<typeof emergencyInfoFormSchema>>({
    resolver: zodResolver(emergencyInfoFormSchema),
    defaultValues: {
      bloodType: "",
      allergies: "",
      chronicConditions: "",
      emergencyContactName: "",
      emergencyContactPhone: "",
    },
  });

  // Set form values when user profile data is loaded
  useEffect(() => {
    if (userProfile) {
      profileForm.reset({
        fullName: userProfile.fullName,
        email: userProfile.email,
        phone: userProfile.phone || "",
        dateOfBirth: userProfile.dateOfBirth || "",
        gender: userProfile.gender || "",
      });

      emergencyForm.reset({
        bloodType: userProfile.bloodType || "",
        allergies: userProfile.allergies || "",
        chronicConditions: userProfile.chronicConditions || "",
        emergencyContactName: userProfile.emergencyContactName || "",
        emergencyContactPhone: userProfile.emergencyContactPhone || "",
      });
    }
  }, [userProfile]);

  const updateProfileMutation = useMutation({
    mutationFn: (data: any) => updateUserProfile(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/users/profile"] });
      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update profile: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const updateEmergencyInfoMutation = useMutation({
    mutationFn: (data: any) => updateEmergencyInfo(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/users/profile"] });
      toast({
        title: "Emergency information updated",
        description: "Your emergency information has been updated successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update emergency information: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const onProfileSubmit = (data: z.infer<typeof profileFormSchema>) => {
    updateProfileMutation.mutate(data);
  };

  const onEmergencyInfoSubmit = (data: z.infer<typeof emergencyInfoFormSchema>) => {
    updateEmergencyInfoMutation.mutate(data);
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-purple-50 to-indigo-100 dark:from-gray-800 dark:via-purple-950 dark:to-blue-950 py-8 px-4 sm:px-6 lg:px-8 transition-all duration-500">
      {/* Background elements */}
      <div className="fixed inset-0 -z-10 overflow-hidden opacity-70 dark:opacity-40">
        <div className="absolute top-1/4 -left-20 w-72 h-72 bg-purple-500/20 dark:bg-purple-500/10 rounded-full filter blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 -right-20 w-72 h-72 bg-blue-500/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* Page Header */}
      <div className="mb-8 relative group">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-indigo-600/5 to-blue-600/5 dark:from-purple-400/10 dark:via-indigo-400/10 dark:to-blue-400/10 rounded-xl transform transition-all duration-300 group-hover:scale-105 group-hover:opacity-80"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-indigo-600/5 to-blue-600/5 dark:from-purple-400/10 dark:via-indigo-400/10 dark:to-blue-400/10 rounded-xl blur-xl opacity-50 transform transition-all duration-300 group-hover:opacity-100"></div>

        <div className="relative bg-white/80 dark:bg-gray-800/80 p-6 rounded-xl backdrop-blur-sm animate-fadeIn border border-white/10 dark:border-white/5 shadow-lg">
          <div className="flex items-center">
            <div className="relative mr-4">
              <div className="absolute inset-0 bg-purple-500/30 dark:bg-purple-400/20 rounded-full filter blur-md animate-pulse-slow"></div>
              <Settings className="h-10 w-10 text-purple-600 dark:text-purple-300 relative z-10 animate-float" />
            </div>

            <div>
              <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-300 dark:to-blue-300 animate-gradient-shift">
                Profile Settings
              </h1>
              <div className="h-1 w-0 bg-gradient-to-r from-purple-500 to-blue-600 dark:from-purple-400 dark:to-blue-400 mt-1 rounded-full animate-expand"></div>

              <p className="mt-3 text-base text-gray-700 dark:text-gray-200">
                Manage your personal information and emergency contacts
              </p>
            </div>
          </div>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <div className="relative">
          <div className="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-purple-200 via-blue-200 to-purple-200 dark:from-purple-800 dark:via-blue-800 dark:to-purple-800"></div>
          <TabsList className="mb-4 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm p-1 rounded-lg border border-white/20 dark:border-gray-700/20 relative z-10">
            <TabsTrigger
              value="personal"
              className={`
                relative overflow-hidden transition-all duration-300
                data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/10 data-[state=active]:to-blue-500/10
                data-[state=active]:dark:from-purple-500/20 data-[state=active]:dark:to-blue-500/20
                data-[state=active]:text-purple-700 data-[state=active]:dark:text-purple-300
                data-[state=active]:shadow-sm
              `}
            >
              <div className="flex items-center">
                <UserIcon className="h-4 w-4 mr-2" />
                Personal Information
              </div>
              {activeTab === "personal" && (
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-purple-500 to-blue-500 dark:from-purple-400 dark:to-blue-400"></div>
              )}
            </TabsTrigger>

            <TabsTrigger
              value="emergency"
              className={`
                relative overflow-hidden transition-all duration-300
                data-[state=active]:bg-gradient-to-r data-[state=active]:from-red-500/10 data-[state=active]:to-orange-500/10
                data-[state=active]:dark:from-red-500/20 data-[state=active]:dark:to-orange-500/20
                data-[state=active]:text-red-700 data-[state=active]:dark:text-red-300
                data-[state=active]:shadow-sm
              `}
            >
              <div className="flex items-center">
                <Shield className="h-4 w-4 mr-2" />
                Emergency Information
              </div>
              {activeTab === "emergency" && (
                <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-red-500 to-orange-500 dark:from-red-400 dark:to-orange-400"></div>
              )}
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="personal" className="animate-fadeIn">
          <Card className="border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-blue-500/5 dark:from-purple-500/10 dark:to-blue-500/10 rounded-lg"></div>

            <CardHeader className="relative z-10 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-900/20 dark:to-blue-900/20">
              <CardTitle className="flex items-center text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-300 dark:to-blue-300">
                <UserIcon className="h-5 w-5 mr-2 text-purple-500 dark:text-purple-400" />
                Personal Information
              </CardTitle>
            </CardHeader>

            <CardContent className="relative z-10 pt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                <div className="md:col-span-1 flex justify-center">
                  {userProfile && (
                    <div className="bg-gradient-to-r from-purple-100 to-blue-100 dark:from-purple-900/30 dark:to-blue-900/30 p-6 rounded-xl shadow-inner">
                      <AvatarUpload
                        currentAvatarUrl={userProfile.avatarUrl}
                        username={userProfile.username}
                        fullName={userProfile.fullName}
                      />
                    </div>
                  )}
                </div>

                <div className="md:col-span-2">
                  <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                      <div className="space-y-2 group">
                        <Label htmlFor="fullName" className="flex items-center text-gray-700 dark:text-gray-300 font-medium">
                          <UserCheck className="h-4 w-4 mr-2 text-purple-500 dark:text-purple-400 opacity-70 group-hover:opacity-100 transition-opacity" />
                          Full Name
                        </Label>
                        <div className="relative">
                          <Input
                            id="fullName"
                            placeholder="Enter your full name"
                            className="pl-10 bg-white/70 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500/20 dark:focus:ring-purple-400/20 transition-all duration-300"
                            {...profileForm.register("fullName")}
                          />
                          <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                        </div>
                        {profileForm.formState.errors.fullName && (
                          <p className="text-sm text-red-500 dark:text-red-400 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {profileForm.formState.errors.fullName.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2 group">
                        <Label htmlFor="email" className="flex items-center text-gray-700 dark:text-gray-300 font-medium">
                          <Mail className="h-4 w-4 mr-2 text-blue-500 dark:text-blue-400 opacity-70 group-hover:opacity-100 transition-opacity" />
                          Email Address
                        </Label>
                        <div className="relative">
                          <Input
                            id="email"
                            type="email"
                            placeholder="Enter your email"
                            className="pl-10 bg-white/70 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 focus:border-blue-500 dark:focus:border-blue-400 focus:ring-blue-500/20 dark:focus:ring-blue-400/20 transition-all duration-300"
                            {...profileForm.register("email")}
                          />
                          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                        </div>
                        {profileForm.formState.errors.email && (
                          <p className="text-sm text-red-500 dark:text-red-400 flex items-center">
                            <AlertCircle className="h-3 w-3 mr-1" />
                            {profileForm.formState.errors.email.message}
                          </p>
                        )}
                      </div>

                      <div className="space-y-2 group">
                        <Label htmlFor="phone" className="flex items-center text-gray-700 dark:text-gray-300 font-medium">
                          <Phone className="h-4 w-4 mr-2 text-green-500 dark:text-green-400 opacity-70 group-hover:opacity-100 transition-opacity" />
                          Phone Number
                        </Label>
                        <div className="relative">
                          <Input
                            id="phone"
                            placeholder="Enter your phone number"
                            className="pl-10 bg-white/70 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500/20 dark:focus:ring-green-400/20 transition-all duration-300"
                            {...profileForm.register("phone")}
                          />
                          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                        </div>
                      </div>

                      <div className="space-y-2 group">
                        <Label htmlFor="dateOfBirth" className="flex items-center text-gray-700 dark:text-gray-300 font-medium">
                          <Calendar className="h-4 w-4 mr-2 text-amber-500 dark:text-amber-400 opacity-70 group-hover:opacity-100 transition-opacity" />
                          Date of Birth
                        </Label>
                        <div className="relative">
                          <Input
                            id="dateOfBirth"
                            type="date"
                            className="pl-10 bg-white/70 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 focus:border-amber-500 dark:focus:border-amber-400 focus:ring-amber-500/20 dark:focus:ring-amber-400/20 transition-all duration-300"
                            {...profileForm.register("dateOfBirth")}
                          />
                          <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                        </div>
                      </div>

                      <div className="space-y-2 group sm:col-span-2">
                        <Label htmlFor="gender" className="flex items-center text-gray-700 dark:text-gray-300 font-medium">
                          <UserIcon className="h-4 w-4 mr-2 text-indigo-500 dark:text-indigo-400 opacity-70 group-hover:opacity-100 transition-opacity" />
                          Gender
                        </Label>
                        <Select
                          value={profileForm.watch("gender")}
                          onValueChange={(value) => profileForm.setValue("gender", value)}
                        >
                          <SelectTrigger className="bg-white/70 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 focus:border-indigo-500 dark:focus:border-indigo-400 focus:ring-indigo-500/20 dark:focus:ring-indigo-400/20 transition-all duration-300">
                            <SelectValue placeholder="Select gender" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Male">Male</SelectItem>
                            <SelectItem value="Female">Female</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                            <SelectItem value="Prefer not to say">Prefer not to say</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                <div className="flex justify-end mt-8">
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 dark:from-purple-400/20 dark:to-blue-400/20 rounded-md blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <Button
                      type="submit"
                      disabled={updateProfileMutation.isPending}
                      className="relative bg-gradient-to-r from-purple-500 to-blue-600 dark:from-purple-400 dark:to-blue-500 hover:from-purple-600 hover:to-blue-700 dark:hover:from-purple-500 dark:hover:to-blue-600 text-white shadow-md hover:shadow-lg transition-all duration-300 border-0"
                    >
                      {updateProfileMutation.isPending ? (
                        <div className="flex items-center">
                          <div className="relative mr-2">
                            <div className="absolute inset-0 bg-white/20 rounded-full blur-sm animate-pulse-slow"></div>
                            <Loader2 className="h-4 w-4 animate-spin relative z-10" />
                          </div>
                          Saving...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <div className="relative mr-2">
                            <div className="absolute inset-0 bg-white/20 rounded-full blur-sm animate-pulse-slow"></div>
                            <Save className="h-4 w-4 relative z-10" />
                          </div>
                          Save Changes
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              </form>
              </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="emergency" className="animate-fadeIn">
          <Card className="border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm shadow-xl overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 to-orange-500/5 dark:from-red-500/10 dark:to-orange-500/10 rounded-lg"></div>

            <CardHeader className="relative z-10 border-b border-gray-100 dark:border-gray-700 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20">
              <CardTitle className="flex items-center text-transparent bg-clip-text bg-gradient-to-r from-red-600 to-orange-600 dark:from-red-300 dark:to-orange-300">
                <Shield className="h-5 w-5 mr-2 text-red-500 dark:text-red-400" />
                Emergency Information
              </CardTitle>
            </CardHeader>

            <CardContent className="relative z-10 pt-6">
              <div className="relative p-0.5 rounded-lg overflow-hidden mb-6">
                <div className="absolute inset-0 bg-gradient-to-r from-red-500 to-orange-500 dark:from-red-600 dark:to-orange-600 animate-pulse-slow rounded-lg"></div>

                <div className="relative bg-red-50 dark:bg-red-900/20 rounded-md p-4 backdrop-blur-sm border border-red-200 dark:border-red-800/30">
                  <div className="flex items-start">
                    <div className="relative mr-3 mt-0.5">
                      <div className="absolute inset-0 bg-red-400/20 dark:bg-red-400/30 rounded-full blur-sm animate-pulse-slow"></div>
                      <AlertCircle className="h-5 w-5 text-red-600 dark:text-red-400 relative z-10" />
                    </div>
                    <div>
                      <h4 className="text-sm font-semibold text-red-700 dark:text-red-300 mb-1">Important Notice</h4>
                      <p className="text-sm text-red-700 dark:text-red-400">
                        This information will be accessible to emergency responders through your emergency QR code.
                        Make sure it's accurate and up-to-date.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
              <form onSubmit={emergencyForm.handleSubmit(onEmergencyInfoSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div className="space-y-2 group">
                    <Label htmlFor="bloodType" className="flex items-center text-gray-700 dark:text-gray-300 font-medium">
                      <Heart className="h-4 w-4 mr-2 text-red-500 dark:text-red-400 opacity-70 group-hover:opacity-100 transition-opacity" />
                      Blood Type
                    </Label>
                    <Select
                      value={emergencyForm.watch("bloodType")}
                      onValueChange={(value) => emergencyForm.setValue("bloodType", value)}
                    >
                      <SelectTrigger className="bg-white/70 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 focus:border-red-500 dark:focus:border-red-400 focus:ring-red-500/20 dark:focus:ring-red-400/20 transition-all duration-300">
                        <SelectValue placeholder="Select blood type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="A+">A+</SelectItem>
                        <SelectItem value="A-">A-</SelectItem>
                        <SelectItem value="B+">B+</SelectItem>
                        <SelectItem value="B-">B-</SelectItem>
                        <SelectItem value="AB+">AB+</SelectItem>
                        <SelectItem value="AB-">AB-</SelectItem>
                        <SelectItem value="O+">O+</SelectItem>
                        <SelectItem value="O-">O-</SelectItem>
                        <SelectItem value="Unknown">Unknown</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-2 group">
                  <Label htmlFor="allergies" className="flex items-center text-gray-700 dark:text-gray-300 font-medium">
                    <AlertCircle className="h-4 w-4 mr-2 text-amber-500 dark:text-amber-400 opacity-70 group-hover:opacity-100 transition-opacity" />
                    Allergies
                  </Label>
                  <div className="relative">
                    <Textarea
                      id="allergies"
                      placeholder="List any allergies (medications, food, etc.)"
                      className="bg-white/70 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 focus:border-amber-500 dark:focus:border-amber-400 focus:ring-amber-500/20 dark:focus:ring-amber-400/20 transition-all duration-300 min-h-[100px]"
                      {...emergencyForm.register("allergies")}
                    />
                    <div className="absolute top-3 right-3 opacity-30 dark:opacity-20">
                      <AlertCircle className="h-16 w-16 text-amber-200 dark:text-amber-800" />
                    </div>
                  </div>
                </div>

                <div className="space-y-2 group">
                  <Label htmlFor="chronicConditions" className="flex items-center text-gray-700 dark:text-gray-300 font-medium">
                    <Activity className="h-4 w-4 mr-2 text-orange-500 dark:text-orange-400 opacity-70 group-hover:opacity-100 transition-opacity" />
                    Chronic Conditions
                  </Label>
                  <div className="relative">
                    <Textarea
                      id="chronicConditions"
                      placeholder="List any chronic conditions (diabetes, asthma, etc.)"
                      className="bg-white/70 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 focus:border-orange-500 dark:focus:border-orange-400 focus:ring-orange-500/20 dark:focus:ring-orange-400/20 transition-all duration-300 min-h-[100px]"
                      {...emergencyForm.register("chronicConditions")}
                    />
                    <div className="absolute top-3 right-3 opacity-30 dark:opacity-20">
                      <Activity className="h-16 w-16 text-orange-200 dark:text-orange-800" />
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <div className="space-y-2 group">
                    <Label htmlFor="emergencyContactName" className="flex items-center text-gray-700 dark:text-gray-300 font-medium">
                      <UserCheck className="h-4 w-4 mr-2 text-green-500 dark:text-green-400 opacity-70 group-hover:opacity-100 transition-opacity" />
                      Emergency Contact Name
                    </Label>
                    <div className="relative">
                      <Input
                        id="emergencyContactName"
                        placeholder="Enter emergency contact name"
                        className="pl-10 bg-white/70 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500/20 dark:focus:ring-green-400/20 transition-all duration-300"
                        {...emergencyForm.register("emergencyContactName")}
                      />
                      <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                    </div>
                  </div>

                  <div className="space-y-2 group">
                    <Label htmlFor="emergencyContactPhone" className="flex items-center text-gray-700 dark:text-gray-300 font-medium">
                      <Phone className="h-4 w-4 mr-2 text-green-500 dark:text-green-400 opacity-70 group-hover:opacity-100 transition-opacity" />
                      Emergency Contact Phone
                    </Label>
                    <div className="relative">
                      <Input
                        id="emergencyContactPhone"
                        placeholder="Enter emergency contact phone"
                        className="pl-10 bg-white/70 dark:bg-gray-800/70 border-gray-200 dark:border-gray-700 focus:border-green-500 dark:focus:border-green-400 focus:ring-green-500/20 dark:focus:ring-green-400/20 transition-all duration-300"
                        {...emergencyForm.register("emergencyContactPhone")}
                      />
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 dark:text-gray-500" />
                    </div>
                  </div>
                </div>
                <div className="flex justify-end mt-8">
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 to-orange-500/20 dark:from-red-400/20 dark:to-orange-400/20 rounded-md blur-md opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <Button
                      type="submit"
                      disabled={updateEmergencyInfoMutation.isPending}
                      className="relative bg-gradient-to-r from-red-500 to-orange-600 dark:from-red-400 dark:to-orange-500 hover:from-red-600 hover:to-orange-700 dark:hover:from-red-500 dark:hover:to-orange-600 text-white shadow-md hover:shadow-lg transition-all duration-300 border-0"
                    >
                      {updateEmergencyInfoMutation.isPending ? (
                        <div className="flex items-center">
                          <div className="relative mr-2">
                            <div className="absolute inset-0 bg-white/20 rounded-full blur-sm animate-pulse-slow"></div>
                            <Loader2 className="h-4 w-4 animate-spin relative z-10" />
                          </div>
                          Saving...
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <div className="relative mr-2">
                            <div className="absolute inset-0 bg-white/20 rounded-full blur-sm animate-pulse-slow"></div>
                            <Shield className="h-4 w-4 relative z-10" />
                          </div>
                          Save Emergency Information
                        </div>
                      )}
                    </Button>
                  </div>
                </div>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

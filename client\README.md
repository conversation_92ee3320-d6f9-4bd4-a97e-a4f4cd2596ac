# MediKey - Single Page Application for GitHub Pages

This is the client-side application for MediKey, configured to run as a Single Page Application (SPA) on GitHub Pages.

## Features

- React-based frontend with TypeScript
- Responsive design for desktop and mobile devices
- Dark mode and light mode support
- Secure authentication system
- Medical records management
- Family member profiles
- Appointment scheduling
- AI-powered medical assistant
- Emergency information with QR code generation

## Deployment to GitHub Pages

### Automatic Deployment (GitHub Actions)

The project is configured to automatically deploy to GitHub Pages when changes are pushed to the main branch. The deployment process is handled by a GitHub Actions workflow defined in `.github/workflows/deploy.yml`.

### Manual Deployment

To manually deploy the application to GitHub Pages:

1. Install dependencies:
   ```
   npm install
   ```

2. Build the application for GitHub Pages:
   ```
   npm run build:github
   ```

3. Deploy to GitHub Pages:
   ```
   npm run deploy
   ```

## Development

To run the application locally:

1. Install dependencies:
   ```
   npm install
   ```

2. Start the development server:
   ```
   npm run dev
   ```

## Configuration

The application is configured to detect whether it's running on GitHub Pages and adjust the base path accordingly. This is handled in:

- `vite.config.ts`: Sets the base path to `/medikey/` when building for GitHub Pages
- `App.tsx`: Adjusts route paths based on the deployment environment
- `config.ts`: Configures API endpoints based on the deployment environment

## SPA Routing on GitHub Pages

The application uses a custom 404.html page and a script in index.html to handle client-side routing on GitHub Pages. This ensures that direct navigation to routes like `/medikey/dashboard` works correctly.

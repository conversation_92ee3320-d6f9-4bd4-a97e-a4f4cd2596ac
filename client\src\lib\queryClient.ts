import { QueryClient, QueryFunction } from "@tanstack/react-query";
import config from "../config";
import { mockApi, shouldUseMockApi } from "./mockApi";

// Check if we should use the mock API (for GitHub Pages)
const useMockApi = shouldUseMockApi();

async function throwIfResNotOk(res: Response) {
  if (!res.ok) {
    const text = (await res.text()) || res.statusText;
    throw new Error(`${res.status}: ${text}`);
  }
}

export async function apiRequest(
  method: string,
  url: string,
  data?: unknown | undefined,
): Promise<Response> {
  // If the URL starts with /api, use the base URL from config
  const fullUrl = url.startsWith('/api')
    ? `${config.apiBaseUrl}${url.substring(4)}`
    : url;

  const res = await fetch(fullUrl, {
    method,
    headers: data ? { "Content-Type": "application/json" } : {},
    body: data ? JSON.stringify(data) : undefined,
    credentials: "include",
  });

  await throwIfResNotOk(res);
  return res;
}

type UnauthorizedBehavior = "returnNull" | "throw";
export const getQueryFn: <T>(options: {
  on401: UnauthorizedBehavior;
}) => QueryFunction<T> =
  ({ on401: unauthorizedBehavior }) =>
  async ({ queryKey }) => {
    const url = queryKey[0] as string;
    const params = queryKey.length > 1 ? queryKey[1] : undefined;

    // If using mock API, handle specific endpoints
    if (useMockApi) {
      console.log("Using mock API for query:", url, params);

      // Handle specific mock endpoints
      if (url === "/api/records") {
        return mockApi.records.getAll();
      }

      if (url === "/api/appointments") {
        return mockApi.appointments.getAll();
      }

      if (url === "/api/health-metrics") {
        return mockApi.healthMetrics.getAll(params as string);
      }

      if (url === "/api/family-members") {
        return mockApi.family.getMembers();
      }

      if (url === "/api/users/profile") {
        return mockApi.profile.get();
      }

      if (url === "/api/emergency/qr-code") {
        return mockApi.emergency.getQRCode();
      }

      if (url === "/api/smartwatch/connected-devices") {
        return mockApi.smartwatch.getConnectedDevices();
      }

      // Default mock response for unhandled endpoints
      console.warn("Unhandled mock API endpoint:", url);
      return { message: "Mock data not implemented for this endpoint" };
    }

    // If not using mock API, proceed with real API request
    // If the URL starts with /api, use the base URL from config
    const fullUrl = url.startsWith('/api')
      ? `${config.apiBaseUrl}${url.substring(4)}`
      : url;

    const res = await fetch(fullUrl, {
      credentials: "include",
    });

    if (unauthorizedBehavior === "returnNull" && res.status === 401) {
      return null;
    }

    await throwIfResNotOk(res);
    return await res.json();
  };

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      queryFn: getQueryFn({ on401: "throw" }),
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: Infinity,
      retry: false,
    },
    mutations: {
      retry: false,
    },
  },
});

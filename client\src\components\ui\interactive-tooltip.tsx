import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  Toolt<PERSON><PERSON><PERSON><PERSON>,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Button } from "@/components/ui/button";
import { Info, X } from "lucide-react";
import { cn } from "@/lib/utils";

interface InteractiveTooltipProps {
  content: React.ReactNode;
  children: React.ReactNode;
  className?: string;
  side?: "top" | "right" | "bottom" | "left";
  interactive?: boolean;
  icon?: React.ReactNode;
  persistent?: boolean;
}

export function InteractiveTooltip({
  content,
  children,
  className,
  side = "top",
  interactive = false,
  icon = <Info className="h-4 w-4" />,
  persistent = false,
}: InteractiveTooltipProps) {
  const [open, setOpen] = useState(false);
  const [dismissed, setDismissed] = useState(false);

  if (dismissed && persistent) {
    return <>{children}</>;
  }

  return (
    <TooltipProvider>
      <Tooltip open={open} onOpenChange={setO<PERSON>} delayDuration={300}>
        <TooltipTrigger asChild>
          <div className="group relative inline-block">
            {children}
            <span className="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-[10px] text-primary-foreground">
              {icon}
            </span>
          </div>
        </TooltipTrigger>
        <TooltipContent
          side={side}
          className={cn(
            "max-w-xs p-4 z-50",
            interactive && "interactive-tooltip",
            className
          )}
        >
          <div className="relative">
            {interactive && persistent && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute -top-2 -right-2 h-5 w-5 rounded-full"
                onClick={() => setDismissed(true)}
              >
                <X className="h-3 w-3" />
              </Button>
            )}
            {content}
            {interactive && (
              <div className="mt-2 flex justify-end">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => setOpen(false)}
                >
                  Got it
                </Button>
              </div>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import {
  Heart, Droplet, Scale, Stethoscope, Activity, Sparkles,
  Brain, ArrowUp, ArrowDown, Calendar, FileText, Zap
} from "lucide-react";
import {
  LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, ResponsiveContainer,
  AreaChart, Area, BarChart, Bar, Legend
} from "recharts";
import { ColorfulButton } from "@/components/ui/colorful-button";
import { ColorfulCard } from "@/components/ui/colorful-card";
import { ParticleBackground } from "@/components/ui/particle-background";
import { InteractiveTooltip } from "@/components/ui/interactive-tooltip";

// Define the structure of the data returned from the API
interface HealthAnalyticsData {
  bloodPressure: {
    latest: string;
    change: number;
    data: {
      date: string;
      systolic: number;
      diastolic: number;
    }[];
  };
  bloodSugar: {
    latest: string;
    change: number;
    data: {
      date: string;
      value: number;
    }[];
  };
  weight: {
    latest: string;
    change: number;
    data: {
      date: string;
      value: number;
      bmi: number;
    }[];
  };
}

export default function Analytics() {
  const [timeRange, setTimeRange] = useState("3m");
  const [animateItems, setAnimateItems] = useState(false);
  const [colorMode, setColorMode] = useState<'blue' | 'purple' | 'green' | 'rainbow'>('purple');
  const [activeTab, setActiveTab] = useState("blood-pressure");

  useEffect(() => {
    // Animate items after a short delay for a staggered effect
    const timer = setTimeout(() => {
      setAnimateItems(true);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  const { data: metricsData, isLoading } = useQuery<HealthAnalyticsData>({
    queryKey: ["/api/health-metrics"],
  });

  // These arrays will contain the charting data
  // Access data from the nested structure
  const bloodPressureData = metricsData?.bloodPressure?.data || [];
  const bloodSugarData = metricsData?.bloodSugar?.data || [];
  const weightData = metricsData?.weight?.data || [];

  // For cholesterol, we'll use an empty array for now as it's not in our mock data
  const cholesterolData: any[] = [];

  // Values for the metrics cards
  const latestBloodPressure = metricsData?.bloodPressure?.latest || null;
  const bloodPressureChange = metricsData?.bloodPressure?.change || 0;

  const latestBloodSugar = metricsData?.bloodSugar?.latest || null;
  const bloodSugarChange = metricsData?.bloodSugar?.change || 0;

  const latestWeight = metricsData?.weight?.latest || null;
  const weightChange = metricsData?.weight?.change || 0;

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-purple-50 to-indigo-100 dark:from-gray-800 dark:via-purple-950 dark:to-blue-950 py-6 px-4 sm:px-6 lg:px-8 transition-all duration-500">
      {/* Particle Background */}
      <ParticleBackground
        colorScheme={colorMode}
        particleCount={80}
        speed={0.3}
        interactive={true}
        density={0.8}
      />

      {/* Animated background glow effects */}
      <div className="fixed inset-0 -z-5 overflow-hidden opacity-70 dark:opacity-40">
        <div className="absolute top-1/4 -left-20 w-72 h-72 bg-purple-500/20 dark:bg-purple-500/10 rounded-full filter blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 -right-20 w-72 h-72 bg-blue-500/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* Page Header */}
      <div className="mb-8 relative group">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-indigo-600/5 to-blue-600/5 dark:from-purple-400/10 dark:via-indigo-400/10 dark:to-blue-400/10 rounded-xl transform transition-all duration-300 group-hover:scale-105 group-hover:opacity-80"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-indigo-600/5 to-blue-600/5 dark:from-purple-400/10 dark:via-indigo-400/10 dark:to-blue-400/10 rounded-xl blur-xl opacity-50 transform transition-all duration-300 group-hover:opacity-100"></div>

        <div className="relative bg-gradient-to-r from-purple-500/10 via-indigo-500/10 to-blue-500/10 dark:from-purple-500/20 dark:via-indigo-500/20 dark:to-blue-500/20 p-6 rounded-xl backdrop-blur-sm animate-fadeIn border border-white/10 dark:border-white/5 shadow-lg">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between">
            <div className="flex items-start">
              <div className="relative mr-4">
                <div className="absolute inset-0 bg-purple-500/30 dark:bg-purple-400/20 rounded-full filter blur-md animate-pulse-slow"></div>
                <Activity className="h-10 w-10 text-purple-600 dark:text-purple-300 relative z-10 animate-float" />
              </div>

              <div className="flex-1">
                <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-300 dark:to-blue-300 animate-gradient-shift">
                  Health Analytics
                </h1>
                <div className="h-1 w-0 bg-gradient-to-r from-purple-500 to-blue-600 dark:from-purple-400 dark:to-blue-400 mt-1 rounded-full animate-expand"></div>

                <p className="mt-3 text-base text-gray-700 dark:text-gray-200">
                  Track and monitor your health metrics over time
                </p>
              </div>
            </div>

            <div className="mt-4 sm:mt-0 flex items-center">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 dark:from-purple-400/20 dark:to-blue-400/20 rounded-lg blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="relative z-10 flex items-center">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-200 mr-2">Time Period:</span>
                  <Select value={timeRange} onValueChange={setTimeRange}>
                    <SelectTrigger className="w-[180px] border-gray-200 dark:border-gray-700 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm focus:ring-purple-500 dark:focus:ring-purple-400">
                      <SelectValue placeholder="Select time range" />
                    </SelectTrigger>
                    <SelectContent className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-gray-200 dark:border-gray-700">
                      <SelectItem value="1m">Last Month</SelectItem>
                      <SelectItem value="3m">Last 3 Months</SelectItem>
                      <SelectItem value="6m">Last 6 Months</SelectItem>
                      <SelectItem value="1y">Last Year</SelectItem>
                      <SelectItem value="all">All Time</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-4 flex flex-wrap gap-3">
            <div className="flex items-center space-x-2 bg-purple-500/10 dark:bg-purple-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.3s' }}>
              <div className="w-2 h-2 rounded-full bg-purple-500 dark:bg-purple-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-purple-700 dark:text-purple-300">Personalized Insights</p>
            </div>

            <div className="flex items-center space-x-2 bg-blue-500/10 dark:bg-blue-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.5s' }}>
              <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-blue-700 dark:text-blue-300">Interactive Charts</p>
            </div>

            <div className="flex items-center space-x-2 bg-green-500/10 dark:bg-green-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.7s' }}>
              <div className="w-2 h-2 rounded-full bg-green-500 dark:bg-green-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-green-700 dark:text-green-300">AI Recommendations</p>
            </div>
          </div>
        </div>
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        {/* Blood Pressure */}
        <div className={`transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '100ms' }}>
          <ColorfulCard
            colorScheme="gradient-pink-purple"
            glowEffect={true}
            hoverAnimation="lift"
            entranceAnimation="fade-up"
            className="overflow-hidden h-full"
          >
            <div className="p-5 bg-gradient-to-br from-pink-100/70 to-purple-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm h-full">
              <div className="flex items-center">
                <div className="relative">
                  <div className="absolute inset-0 bg-pink-500/30 dark:bg-pink-400/20 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="h-12 w-12 rounded-full bg-gradient-to-br from-pink-500 to-purple-500 dark:from-pink-400 dark:to-purple-400 flex items-center justify-center shadow-lg shadow-pink-500/20 relative z-10">
                    <Heart className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-4 flex-1">
                  <h3 className="text-sm font-medium text-pink-700 dark:text-pink-300 truncate">Blood Pressure</h3>
                  {latestBloodPressure ? (
                    <div>
                      <p className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600 dark:from-pink-300 dark:to-purple-300">
                        {latestBloodPressure}
                      </p>
                      <div className="flex items-center mt-1">
                        <div className={`flex items-center text-sm ${bloodPressureChange > 0 ? 'text-red-500 dark:text-red-400' : 'text-green-500 dark:text-green-400'}`}>
                          {bloodPressureChange > 0 ? (
                            <ArrowUp className="h-3 w-3 mr-1" />
                          ) : (
                            <ArrowDown className="h-3 w-3 mr-1" />
                          )}
                          <span>{Math.abs(bloodPressureChange)}%</span>
                        </div>
                        <span className="mx-2 text-gray-400 dark:text-gray-500">•</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">from last month</span>
                      </div>
                    </div>
                  ) : (
                    <p className="text-lg text-gray-500 dark:text-gray-400">No data</p>
                  )}
                </div>
              </div>
            </div>
          </ColorfulCard>
        </div>

        {/* Blood Sugar */}
        <div className={`transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '200ms' }}>
          <ColorfulCard
            colorScheme="gradient-blue-cyan"
            glowEffect={true}
            hoverAnimation="lift"
            entranceAnimation="fade-up"
            className="overflow-hidden h-full"
          >
            <div className="p-5 bg-gradient-to-br from-blue-100/70 to-cyan-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm h-full">
              <div className="flex items-center">
                <div className="relative">
                  <div className="absolute inset-0 bg-blue-500/30 dark:bg-blue-400/20 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="h-12 w-12 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 dark:from-blue-400 dark:to-cyan-400 flex items-center justify-center shadow-lg shadow-blue-500/20 relative z-10">
                    <Droplet className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-4 flex-1">
                  <h3 className="text-sm font-medium text-blue-700 dark:text-blue-300 truncate">Blood Sugar</h3>
                  {latestBloodSugar ? (
                    <div>
                      <p className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-300 dark:to-cyan-300">
                        {latestBloodSugar}
                      </p>
                      <div className="flex items-center mt-1">
                        <div className={`flex items-center text-sm ${bloodSugarChange > 0 ? 'text-red-500 dark:text-red-400' : 'text-green-500 dark:text-green-400'}`}>
                          {bloodSugarChange > 0 ? (
                            <ArrowUp className="h-3 w-3 mr-1" />
                          ) : (
                            <ArrowDown className="h-3 w-3 mr-1" />
                          )}
                          <span>{Math.abs(bloodSugarChange)}%</span>
                        </div>
                        <span className="mx-2 text-gray-400 dark:text-gray-500">•</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">from last month</span>
                      </div>
                    </div>
                  ) : (
                    <p className="text-lg text-gray-500 dark:text-gray-400">No data</p>
                  )}
                </div>
              </div>
            </div>
          </ColorfulCard>
        </div>

        {/* Weight/BMI */}
        <div className={`transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '300ms' }}>
          <ColorfulCard
            colorScheme="gradient-green-blue"
            glowEffect={true}
            hoverAnimation="lift"
            entranceAnimation="fade-up"
            className="overflow-hidden h-full"
          >
            <div className="p-5 bg-gradient-to-br from-green-100/70 to-blue-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm h-full">
              <div className="flex items-center">
                <div className="relative">
                  <div className="absolute inset-0 bg-green-500/30 dark:bg-green-400/20 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="h-12 w-12 rounded-full bg-gradient-to-br from-green-500 to-blue-500 dark:from-green-400 dark:to-blue-400 flex items-center justify-center shadow-lg shadow-green-500/20 relative z-10">
                    <Scale className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-4 flex-1">
                  <h3 className="text-sm font-medium text-green-700 dark:text-green-300 truncate">Weight / BMI</h3>
                  {latestWeight ? (
                    <div>
                      <p className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600 dark:from-green-300 dark:to-blue-300">
                        {latestWeight}
                      </p>
                      <div className="flex items-center mt-1">
                        <div className={`flex items-center text-sm ${weightChange > 0 ? 'text-red-500 dark:text-red-400' : 'text-green-500 dark:text-green-400'}`}>
                          {weightChange > 0 ? (
                            <ArrowUp className="h-3 w-3 mr-1" />
                          ) : (
                            <ArrowDown className="h-3 w-3 mr-1" />
                          )}
                          <span>{Math.abs(weightChange)}%</span>
                        </div>
                        <span className="mx-2 text-gray-400 dark:text-gray-500">•</span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">from last month</span>
                      </div>
                    </div>
                  ) : (
                    <p className="text-lg text-gray-500 dark:text-gray-400">No data</p>
                  )}
                </div>
              </div>
            </div>
          </ColorfulCard>
        </div>

        {/* Overall Health Score */}
        <div className={`transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '400ms' }}>
          <ColorfulCard
            colorScheme="gradient-yellow-orange"
            glowEffect={true}
            hoverAnimation="lift"
            entranceAnimation="fade-up"
            className="overflow-hidden h-full"
          >
            <div className="p-5 bg-gradient-to-br from-yellow-100/70 to-orange-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm h-full">
              <div className="flex items-center">
                <div className="relative">
                  <div className="absolute inset-0 bg-yellow-500/30 dark:bg-yellow-400/20 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="h-12 w-12 rounded-full bg-gradient-to-br from-yellow-500 to-orange-500 dark:from-yellow-400 dark:to-orange-400 flex items-center justify-center shadow-lg shadow-yellow-500/20 relative z-10">
                    <Activity className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-4 flex-1">
                  <h3 className="text-sm font-medium text-yellow-700 dark:text-yellow-300 truncate">Health Score</h3>
                  <div>
                    <p className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-600 to-orange-600 dark:from-yellow-300 dark:to-orange-300">
                      78/100
                    </p>
                    <div className="flex items-center mt-1">
                      <div className="flex items-center text-sm text-green-500 dark:text-green-400">
                        <ArrowUp className="h-3 w-3 mr-1" />
                        <span>5%</span>
                      </div>
                      <span className="mx-2 text-gray-400 dark:text-gray-500">•</span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">from last month</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ColorfulCard>
        </div>
      </div>

      {/* Detailed Charts */}
      <ColorfulCard
        className="overflow-hidden animate-fadeIn"
        colorScheme="gradient-purple-blue"
        glowEffect={true}
        entranceAnimation="fade-up"
      >
        <div className="p-4 bg-gradient-to-br from-purple-100/70 to-blue-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm">
          <Tabs
            defaultValue="blood-pressure"
            className="w-full"
            onValueChange={(value) => setActiveTab(value)}
          >
            <div className="relative">
              <div className="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-purple-200 via-blue-200 to-purple-200 dark:from-purple-800 dark:via-blue-800 dark:to-purple-800"></div>
              <TabsList className="mb-4 bg-transparent relative z-10 space-x-2">
                {[
                  { value: "blood-pressure", label: "Blood Pressure", icon: <Heart className="h-4 w-4 mr-1" /> },
                  { value: "blood-sugar", label: "Blood Sugar", icon: <Droplet className="h-4 w-4 mr-1" /> },
                  { value: "weight", label: "Weight & BMI", icon: <Scale className="h-4 w-4 mr-1" /> },
                  { value: "cholesterol", label: "Cholesterol", icon: <Activity className="h-4 w-4 mr-1" /> }
                ].map((tab) => (
                  <TabsTrigger
                    key={tab.value}
                    value={tab.value}
                    className={`
                      relative overflow-hidden transition-all duration-300
                      data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/10 data-[state=active]:to-blue-500/10
                      data-[state=active]:dark:from-purple-500/20 data-[state=active]:dark:to-blue-500/20
                      data-[state=active]:text-purple-700 data-[state=active]:dark:text-purple-300
                      data-[state=active]:border-b-2 data-[state=active]:border-purple-500 data-[state=active]:dark:border-purple-400
                      data-[state=active]:shadow-sm
                    `}
                  >
                    <div className="flex items-center">
                      {tab.icon}
                      {tab.label}
                    </div>
                    {activeTab === tab.value && (
                      <div className="absolute bottom-0 left-0 w-full h-0.5 bg-gradient-to-r from-purple-500 to-blue-500 dark:from-purple-400 dark:to-blue-400"></div>
                    )}
                  </TabsTrigger>
                ))}
              </TabsList>
            </div>

            <TabsContent value="blood-pressure" className={`transition-all duration-300 ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm p-4 backdrop-blur-sm">
                <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-pink-600 to-purple-600 dark:from-pink-300 dark:to-purple-300">
                      Blood Pressure Trends
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Systolic and diastolic readings over time (mmHg)
                    </p>
                  </div>
                  <div className="mt-2 md:mt-0 flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-pink-500 dark:bg-pink-400"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">Systolic</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-purple-500 dark:bg-purple-400"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">Diastolic</span>
                    </div>
                  </div>
                </div>

                <div className="h-80 relative">
                  {bloodPressureData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={bloodPressureData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                        <defs>
                          <linearGradient id="systolicGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#EC4899" stopOpacity={0.3} />
                            <stop offset="95%" stopColor="#EC4899" stopOpacity={0} />
                          </linearGradient>
                          <linearGradient id="diastolicGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#8B5CF6" stopOpacity={0.3} />
                            <stop offset="95%" stopColor="#8B5CF6" stopOpacity={0} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                        <XAxis
                          dataKey="date"
                          stroke="#6B7280"
                          fontSize={12}
                          tickLine={false}
                        />
                        <YAxis
                          domain={[60, 160]}
                          stroke="#6B7280"
                          fontSize={12}
                          tickLine={false}
                        />
                        <RechartsTooltip
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            borderRadius: '0.5rem',
                            border: 'none',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                          }}
                        />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="systolic"
                          stroke="#EC4899"
                          strokeWidth={3}
                          dot={{ stroke: '#EC4899', strokeWidth: 2, r: 4, fill: 'white' }}
                          activeDot={{ stroke: '#EC4899', strokeWidth: 2, r: 6, fill: 'white' }}
                          name="Systolic"
                        />
                        <Line
                          type="monotone"
                          dataKey="diastolic"
                          stroke="#8B5CF6"
                          strokeWidth={3}
                          dot={{ stroke: '#8B5CF6', strokeWidth: 2, r: 4, fill: 'white' }}
                          activeDot={{ stroke: '#8B5CF6', strokeWidth: 2, r: 6, fill: 'white' }}
                          name="Diastolic"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex h-full items-center justify-center">
                      <div className="text-center">
                        <Activity className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                        <p className="text-gray-500 dark:text-gray-400">No blood pressure data available</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="blood-sugar" className={`transition-all duration-300 ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm p-4 backdrop-blur-sm">
                <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-cyan-600 dark:from-blue-300 dark:to-cyan-300">
                      Blood Sugar Trends
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Fasting blood glucose readings over time (mg/dL)
                    </p>
                  </div>
                  <div className="mt-2 md:mt-0 flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-blue-500 dark:bg-blue-400"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">Glucose Level</span>
                    </div>
                  </div>
                </div>

                <div className="h-80 relative">
                  {bloodSugarData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <AreaChart data={bloodSugarData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                        <defs>
                          <linearGradient id="bloodSugarGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                            <stop offset="95%" stopColor="#3B82F6" stopOpacity={0} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                        <XAxis
                          dataKey="date"
                          stroke="#6B7280"
                          fontSize={12}
                          tickLine={false}
                        />
                        <YAxis
                          domain={[70, 180]}
                          stroke="#6B7280"
                          fontSize={12}
                          tickLine={false}
                        />
                        <RechartsTooltip
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            borderRadius: '0.5rem',
                            border: 'none',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                          }}
                        />
                        <Area
                          type="monotone"
                          dataKey="value"
                          stroke="#3B82F6"
                          fill="url(#bloodSugarGradient)"
                          strokeWidth={3}
                          dot={{ stroke: '#3B82F6', strokeWidth: 2, r: 4, fill: 'white' }}
                          activeDot={{ stroke: '#3B82F6', strokeWidth: 2, r: 6, fill: 'white' }}
                          name="Glucose Level"
                        />
                      </AreaChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex h-full items-center justify-center">
                      <div className="text-center">
                        <Droplet className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                        <p className="text-gray-500 dark:text-gray-400">No blood sugar data available</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="weight" className={`transition-all duration-300 ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm p-4 backdrop-blur-sm">
                <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600 dark:from-green-300 dark:to-blue-300">
                      Weight & BMI Trends
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Weight (lbs) and BMI measurements over time
                    </p>
                  </div>
                  <div className="mt-2 md:mt-0 flex items-center space-x-2">
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-green-500 dark:bg-green-400"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">Weight</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-blue-500 dark:bg-blue-400"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">BMI</span>
                    </div>
                  </div>
                </div>

                <div className="h-80 relative">
                  {weightData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={weightData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                        <defs>
                          <linearGradient id="weightGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#10B981" stopOpacity={0.3} />
                            <stop offset="95%" stopColor="#10B981" stopOpacity={0} />
                          </linearGradient>
                          <linearGradient id="bmiGradient" x1="0" y1="0" x2="0" y2="1">
                            <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                            <stop offset="95%" stopColor="#3B82F6" stopOpacity={0} />
                          </linearGradient>
                        </defs>
                        <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                        <XAxis
                          dataKey="date"
                          stroke="#6B7280"
                          fontSize={12}
                          tickLine={false}
                        />
                        <YAxis
                          yAxisId="left"
                          stroke="#6B7280"
                          fontSize={12}
                          tickLine={false}
                        />
                        <YAxis
                          yAxisId="right"
                          orientation="right"
                          domain={[15, 35]}
                          stroke="#6B7280"
                          fontSize={12}
                          tickLine={false}
                        />
                        <RechartsTooltip
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            borderRadius: '0.5rem',
                            border: 'none',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                          }}
                        />
                        <Legend />
                        <Line
                          yAxisId="left"
                          type="monotone"
                          dataKey="weight"
                          stroke="#10B981"
                          strokeWidth={3}
                          dot={{ stroke: '#10B981', strokeWidth: 2, r: 4, fill: 'white' }}
                          activeDot={{ stroke: '#10B981', strokeWidth: 2, r: 6, fill: 'white' }}
                          name="Weight"
                        />
                        <Line
                          yAxisId="right"
                          type="monotone"
                          dataKey="bmi"
                          stroke="#3B82F6"
                          strokeWidth={3}
                          dot={{ stroke: '#3B82F6', strokeWidth: 2, r: 4, fill: 'white' }}
                          activeDot={{ stroke: '#3B82F6', strokeWidth: 2, r: 6, fill: 'white' }}
                          name="BMI"
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex h-full items-center justify-center">
                      <div className="text-center">
                        <Scale className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                        <p className="text-gray-500 dark:text-gray-400">No weight data available</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>

            <TabsContent value="cholesterol" className={`transition-all duration-300 ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}>
              <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg shadow-sm p-4 backdrop-blur-sm">
                <div className="flex flex-col md:flex-row md:items-center justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-600 to-orange-600 dark:from-yellow-300 dark:to-orange-300">
                      Cholesterol Profile
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      LDL, HDL, total cholesterol and triglycerides (mg/dL)
                    </p>
                  </div>
                  <div className="mt-2 md:mt-0 flex flex-wrap items-center gap-2">
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-red-500 dark:bg-red-400"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">LDL</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-green-500 dark:bg-green-400"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">HDL</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-indigo-500 dark:bg-indigo-400"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">Total</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <div className="w-3 h-3 rounded-full bg-amber-500 dark:bg-amber-400"></div>
                      <span className="text-xs text-gray-600 dark:text-gray-300">Triglycerides</span>
                    </div>
                  </div>
                </div>

                <div className="h-80 relative">
                  {cholesterolData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={cholesterolData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                        <XAxis
                          dataKey="date"
                          stroke="#6B7280"
                          fontSize={12}
                          tickLine={false}
                        />
                        <YAxis
                          stroke="#6B7280"
                          fontSize={12}
                          tickLine={false}
                        />
                        <RechartsTooltip
                          contentStyle={{
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            borderRadius: '0.5rem',
                            border: 'none',
                            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
                          }}
                        />
                        <Legend />
                        <Bar dataKey="ldl" fill="#EF4444" name="LDL" radius={[4, 4, 0, 0]} />
                        <Bar dataKey="hdl" fill="#10B981" name="HDL" radius={[4, 4, 0, 0]} />
                        <Bar dataKey="total" fill="#6366F1" name="Total" radius={[4, 4, 0, 0]} />
                        <Bar dataKey="triglycerides" fill="#F59E0B" name="Triglycerides" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex h-full items-center justify-center">
                      <div className="text-center">
                        <Heart className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                        <p className="text-gray-500 dark:text-gray-400">No cholesterol data available</p>
                        <p className="text-xs text-gray-400 dark:text-gray-500 mt-2">Schedule your next cholesterol screening</p>
                        <ColorfulButton
                          colorScheme="gradient-yellow-orange"
                          className="mt-4 text-sm"
                          hoverAnimation="scale"
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          Schedule Test
                        </ColorfulButton>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </ColorfulCard>

      {/* Health Recommendations */}
      <div className={`mt-8 transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '500ms' }}>
        <ColorfulCard
          colorScheme="gradient-purple-blue"
          glowEffect={true}
          hoverAnimation="lift"
          entranceAnimation="fade-up"
          className="overflow-hidden"
        >
          <div className="bg-gradient-to-r from-purple-500/10 to-blue-500/10 dark:from-purple-500/20 dark:to-blue-500/20 p-3 border-b border-purple-200 dark:border-purple-800">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-300 dark:to-blue-300 flex items-center">
                <div className="relative mr-2">
                  <div className="absolute inset-0 bg-purple-400/20 dark:bg-purple-400/30 rounded-full blur-sm animate-pulse-slow"></div>
                  <Brain className="h-5 w-5 text-purple-700 dark:text-purple-300 relative z-10" />
                </div>
                Health Insights & Recommendations
              </h2>
              <div className="flex items-center space-x-1 bg-purple-100 dark:bg-purple-900/30 px-2 py-0.5 rounded text-xs text-purple-600 dark:text-purple-300">
                <Sparkles className="h-3 w-3 mr-1" />
                <span>AI-Powered</span>
              </div>
            </div>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-300 pl-7">
              AI-generated insights based on your health metrics
            </p>
          </div>

          <div className="p-5 bg-gradient-to-br from-purple-100/70 to-blue-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm">
            <div className="space-y-5">
              <div className={`transition-all duration-300 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '600ms' }}>
                <div className="flex items-start">
                  <div className="relative">
                    <div className="absolute inset-0 bg-pink-500/20 dark:bg-pink-500/30 rounded-full blur-md animate-pulse-slow"></div>
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-pink-500 to-purple-500 dark:from-pink-400 dark:to-purple-400 flex items-center justify-center shadow-lg shadow-pink-500/20 relative z-10">
                      <Heart className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="ml-4 flex-1">
                    <h4 className="text-base font-medium text-pink-700 dark:text-pink-300">Blood Pressure</h4>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                      Your blood pressure readings are showing improvement over the past 3 months, with a 4% reduction in systolic pressure.
                      Continue with your current medication regimen and consider maintaining or slightly increasing your physical activity.
                    </p>
                    <div className="mt-2 flex items-center">
                      <div className="h-1.5 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-pink-500 to-purple-500 dark:from-pink-400 dark:to-purple-400 rounded-full" style={{ width: '70%' }}></div>
                      </div>
                      <span className="ml-2 text-xs font-medium text-pink-600 dark:text-pink-300">70%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className={`transition-all duration-300 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '700ms' }}>
                <div className="flex items-start">
                  <div className="relative">
                    <div className="absolute inset-0 bg-blue-500/20 dark:bg-blue-500/30 rounded-full blur-md animate-pulse-slow"></div>
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 dark:from-blue-400 dark:to-cyan-400 flex items-center justify-center shadow-lg shadow-blue-500/20 relative z-10">
                      <Droplet className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="ml-4 flex-1">
                    <h4 className="text-base font-medium text-blue-700 dark:text-blue-300">Blood Sugar</h4>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                      Recent fasting blood sugar values are consistently within the normal range (70-100 mg/dL),
                      indicating good glycemic control. Continue with your current diet and monitoring schedule.
                    </p>
                    <div className="mt-2 flex items-center">
                      <div className="h-1.5 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-blue-500 to-cyan-500 dark:from-blue-400 dark:to-cyan-400 rounded-full" style={{ width: '90%' }}></div>
                      </div>
                      <span className="ml-2 text-xs font-medium text-blue-600 dark:text-blue-300">90%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className={`transition-all duration-300 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '800ms' }}>
                <div className="flex items-start">
                  <div className="relative">
                    <div className="absolute inset-0 bg-green-500/20 dark:bg-green-500/30 rounded-full blur-md animate-pulse-slow"></div>
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-green-500 to-blue-500 dark:from-green-400 dark:to-blue-400 flex items-center justify-center shadow-lg shadow-green-500/20 relative z-10">
                      <Scale className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="ml-4 flex-1">
                    <h4 className="text-base font-medium text-green-700 dark:text-green-300">Weight & BMI</h4>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                      Your BMI has decreased from 28.5 to 27.2, showing positive progress.
                      Consider scheduling your next cholesterol screening as your last one was over 6 months ago.
                    </p>
                    <div className="mt-2 flex items-center">
                      <div className="h-1.5 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                        <div className="h-full bg-gradient-to-r from-green-500 to-blue-500 dark:from-green-400 dark:to-blue-400 rounded-full" style={{ width: '85%' }}></div>
                      </div>
                      <span className="ml-2 text-xs font-medium text-green-600 dark:text-green-300">85%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className={`transition-all duration-300 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '900ms' }}>
                <div className="flex items-start">
                  <div className="relative">
                    <div className="absolute inset-0 bg-yellow-500/20 dark:bg-yellow-500/30 rounded-full blur-md animate-pulse-slow"></div>
                    <div className="h-10 w-10 rounded-full bg-gradient-to-br from-yellow-500 to-orange-500 dark:from-yellow-400 dark:to-orange-400 flex items-center justify-center shadow-lg shadow-yellow-500/20 relative z-10">
                      <Stethoscope className="h-5 w-5 text-white" />
                    </div>
                  </div>
                  <div className="ml-4 flex-1">
                    <h4 className="text-base font-medium text-yellow-700 dark:text-yellow-300">Preventive Care</h4>
                    <p className="mt-1 text-sm text-gray-600 dark:text-gray-300">
                      Your next annual physical is due in 3 months. Schedule your appointment to stay on top of your health.
                    </p>
                    <div className="mt-2 flex">
                      <ColorfulButton
                        colorScheme="gradient-yellow-orange"
                        className="text-xs"
                        hoverAnimation="scale"
                      >
                        <Calendar className="h-3 w-3 mr-1" />
                        Schedule Appointment
                      </ColorfulButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ColorfulCard>
      </div>
    </div>
  );
}

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import animation utilities */
@import url('./lib/animation-utils.css');

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;

    /* Custom color variables */
    --blue-light: 221.2 83.2% 53.3%;
    --purple-light: 262.1 83.3% 57.8%;
    --green-light: 142.1 76.2% 36.3%;
    --pink-light: 330.1 81.2% 60.2%;
    --orange-light: 24.6 95% 53.1%;
    --red-light: 0 84.2% 60.2%;

    /* Gradient variables */
    --gradient-blue-purple: linear-gradient(135deg, rgba(99, 102, 241, 1) 0%, rgba(168, 85, 247, 1) 100%);
    --gradient-green-blue: linear-gradient(135deg, rgba(16, 185, 129, 1) 0%, rgba(59, 130, 246, 1) 100%);
    --gradient-orange-red: linear-gradient(135deg, rgba(249, 115, 22, 1) 0%, rgba(239, 68, 68, 1) 100%);
    --gradient-pink-purple: linear-gradient(135deg, rgba(236, 72, 153, 1) 0%, rgba(139, 92, 246, 1) 100%);
    --gradient-cyan-blue: linear-gradient(135deg, rgba(6, 182, 212, 1) 0%, rgba(59, 130, 246, 1) 100%);
    --gradient-yellow-orange: linear-gradient(135deg, rgba(250, 204, 21, 1) 0%, rgba(249, 115, 22, 1) 100%);
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 48%;

    /* Custom color variables - dark mode */
    --blue-light: 217.2 91.2% 59.8%;
    --purple-light: 262.1 83.3% 62.4%;
    --green-light: 142.1 70.6% 45.3%;
    --pink-light: 330.1 81.2% 65.1%;
    --orange-light: 24.6 95% 58.0%;
    --red-light: 0 72.2% 50.6%;
  }

  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slideInUp {
  animation: slideInUp 0.5s ease-out forwards;
}

.animate-pulse-slow {
  animation: pulse 3s ease-in-out infinite;
}

/* Staggered animation delays */
.stagger-1 { animation-delay: 100ms; }
.stagger-2 { animation-delay: 200ms; }
.stagger-3 { animation-delay: 300ms; }
.stagger-4 { animation-delay: 400ms; }
.stagger-5 { animation-delay: 500ms; }
.stagger-6 { animation-delay: 600ms; }

/* Colorful backgrounds */
.bg-blue-gradient {
  background: var(--gradient-blue-purple);
}

.bg-green-gradient {
  background: var(--gradient-green-blue);
}

.bg-orange-gradient {
  background: var(--gradient-orange-red);
}

.bg-pink-gradient {
  background: var(--gradient-pink-purple);
}

/* Colorful text */
.text-gradient {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.text-blue-gradient {
  background-image: var(--gradient-blue-purple);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.text-green-gradient {
  background-image: var(--gradient-green-blue);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.text-orange-gradient {
  background-image: var(--gradient-orange-red);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}

.text-pink-gradient {
  background-image: var(--gradient-pink-purple);
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
}
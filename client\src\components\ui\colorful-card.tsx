import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card";
import { cn } from "@/lib/utils";

type ColorScheme = 
  | "blue" 
  | "purple" 
  | "green" 
  | "pink" 
  | "orange" 
  | "red" 
  | "gradient-blue-purple" 
  | "gradient-green-blue" 
  | "gradient-orange-red" 
  | "gradient-pink-purple"
  | "gradient-cyan-blue"
  | "gradient-yellow-orange";

interface ColorfulCardProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
  colorScheme?: ColorScheme;
  glowEffect?: boolean;
  floatEffect?: boolean;
  borderGradient?: boolean;
  hoverAnimation?: "scale" | "rotate" | "lift" | "none";
  className?: string;
  contentClassName?: string;
  headerClassName?: string;
  footerClassName?: string;
  animationDelay?: number;
  entranceAnimation?: "fade-up" | "fade-down" | "fade-left" | "fade-right" | "scale" | "none";
}

export function ColorfulCard({
  children,
  header,
  footer,
  colorScheme = "blue",
  glowEffect = false,
  floatEffect = false,
  borderGradient = false,
  hoverAnimation = "none",
  className,
  contentClassName,
  headerClassName,
  footerClassName,
  animationDelay = 0,
  entranceAnimation = "none",
  ...props
}: ColorfulCardProps) {
  
  const getColorClasses = () => {
    if (borderGradient) {
      switch (colorScheme) {
        case "blue":
        case "gradient-cyan-blue":
        case "gradient-green-blue":
          return "border-blue-500 dark:border-blue-400";
        case "purple":
        case "gradient-blue-purple":
        case "gradient-pink-purple":
          return "border-purple-500 dark:border-purple-400";
        case "green":
          return "border-green-500 dark:border-green-400";
        case "pink":
          return "border-pink-500 dark:border-pink-400";
        case "orange":
        case "gradient-yellow-orange":
          return "border-orange-500 dark:border-orange-400";
        case "red":
        case "gradient-orange-red":
          return "border-red-500 dark:border-red-400";
        default:
          return "border-blue-500 dark:border-blue-400";
      }
    }
    
    if (colorScheme.startsWith("gradient")) {
      return colorScheme === "gradient-blue-purple" ? "border-gradient-blue-purple" :
             colorScheme === "gradient-green-blue" ? "border-gradient-green-blue" :
             colorScheme === "gradient-orange-red" ? "border-gradient-orange-red" :
             "border-gradient-blue-purple";
    }
    
    return "";
  };

  const getHeaderColorClasses = () => {
    switch (colorScheme) {
      case "blue":
        return "bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300";
      case "purple":
        return "bg-purple-50 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300";
      case "green":
        return "bg-green-50 dark:bg-green-900/20 text-green-800 dark:text-green-300";
      case "pink":
        return "bg-pink-50 dark:bg-pink-900/20 text-pink-800 dark:text-pink-300";
      case "orange":
        return "bg-orange-50 dark:bg-orange-900/20 text-orange-800 dark:text-orange-300";
      case "red":
        return "bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-300";
      case "gradient-blue-purple":
        return "bg-gradient-blue-purple text-white";
      case "gradient-green-blue":
        return "bg-gradient-green-blue text-white";
      case "gradient-orange-red":
        return "bg-gradient-orange-red text-white";
      case "gradient-pink-purple":
        return "bg-gradient-pink-purple text-white";
      case "gradient-cyan-blue":
        return "bg-gradient-cyan-blue text-white";
      case "gradient-yellow-orange":
        return "bg-gradient-yellow-orange text-white";
      default:
        return "bg-blue-50 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300";
    }
  };

  const getHoverClass = () => {
    switch (hoverAnimation) {
      case "scale":
        return "hover-scale";
      case "rotate":
        return "hover-rotate";
      case "lift":
        return "hover-lift";
      case "none":
        return "";
      default:
        return "";
    }
  };

  const getShadowClass = () => {
    if (!glowEffect) return "";
    
    switch (colorScheme) {
      case "blue":
      case "gradient-cyan-blue":
      case "gradient-green-blue":
        return "shadow-blue";
      case "purple":
      case "gradient-blue-purple":
      case "gradient-pink-purple":
        return "shadow-purple";
      case "green":
        return "shadow-green";
      case "pink":
        return "shadow-pink";
      case "orange":
      case "gradient-yellow-orange":
        return "shadow-orange";
      case "red":
      case "gradient-orange-red":
        return "shadow-red";
      default:
        return "shadow-blue";
    }
  };

  const getEntranceAnimation = () => {
    switch (entranceAnimation) {
      case "fade-up":
        return "animate-fade-in-up";
      case "fade-down":
        return "animate-fade-in-down";
      case "fade-left":
        return "animate-fade-in-left";
      case "fade-right":
        return "animate-fade-in-right";
      case "scale":
        return "animate-scale-in";
      case "none":
        return "";
      default:
        return "";
    }
  };

  const getAnimationDelay = () => {
    if (animationDelay === 0) return "";
    return `delay-${animationDelay}`;
  };

  return (
    <Card
      className={cn(
        "transition-all duration-300 border-2",
        getColorClasses(),
        getHoverClass(),
        getShadowClass(),
        floatEffect && "animate-float",
        getEntranceAnimation(),
        getAnimationDelay(),
        className
      )}
      {...props}
    >
      {header && (
        <CardHeader className={cn(
          "transition-colors duration-300",
          colorScheme.startsWith("gradient") ? getHeaderColorClasses() : "",
          headerClassName
        )}>
          {header}
        </CardHeader>
      )}
      <CardContent className={cn(
        "transition-colors duration-300",
        contentClassName
      )}>
        {children}
      </CardContent>
      {footer && (
        <CardFooter className={cn(
          "transition-colors duration-300",
          footerClassName
        )}>
          {footer}
        </CardFooter>
      )}
    </Card>
  );
}

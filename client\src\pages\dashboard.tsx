import EmergencyInfoCard from "@/components/dashboard/EmergencyInfoCard";
import MedicalSummaryCard from "@/components/dashboard/MedicalSummaryCard";
import AppointmentsCard from "@/components/dashboard/AppointmentsCard";
import HealthAnalyticsSection from "@/components/dashboard/HealthAnalyticsSection";
import RecentMedicalRecordsSection from "@/components/dashboard/RecentMedicalRecordsSection";
import FamilyVaultSection from "@/components/dashboard/FamilyVaultSection";
import AIAssistantCard from "@/components/dashboard/AIAssistantCard";
import { LiveHealthMetrics, SmartWatchIntegration } from "@/components/smartwatch";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/hooks/useAuth";
import { Shield, Calendar, Heart, FileText, Users, Bot, Watch, Sparkles, Activity, Star, Zap } from "lucide-react";
import { Link } from "wouter";
import { InteractiveTooltip } from "@/components/ui/interactive-tooltip";
import { useState, useEffect } from "react";
import { ColorfulButton } from "@/components/ui/colorful-button";
import { ColorfulCard } from "@/components/ui/colorful-card";
import { ParticleBackground } from "@/components/ui/particle-background";
import { Confetti } from "@/components/ui/confetti";
import { useToast } from "@/hooks/use-toast";

export default function Dashboard() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [showWelcome, setShowWelcome] = useState(true);
  const [animateItems, setAnimateItems] = useState(false);
  // Track active section for UI feedback
  const [, setActiveSection] = useState<string | null>(null);
  const [colorMode, setColorMode] = useState<'blue' | 'purple' | 'green' | 'rainbow'>('rainbow');
  const [showConfetti, setShowConfetti] = useState(false);

  const { data: userProfile, isLoading: userLoading } = useQuery({
    queryKey: ["/api/users/profile"],
  });

  useEffect(() => {
    // Animate items after a short delay for a staggered effect
    const timer = setTimeout(() => {
      setAnimateItems(true);
    }, 300);

    // Hide welcome message after 5 seconds
    const welcomeTimer = setTimeout(() => {
      setShowWelcome(false);
    }, 8000);

    // Cycle through color modes
    const colorTimer = setInterval(() => {
      setColorMode(prev => {
        if (prev === 'blue') return 'purple';
        if (prev === 'purple') return 'green';
        if (prev === 'green') return 'rainbow';
        return 'blue';
      });
    }, 20000);

    return () => {
      clearTimeout(timer);
      clearTimeout(welcomeTimer);
      clearInterval(colorTimer);
    };
  }, []);

  const handleSectionHover = (section: string) => {
    setActiveSection(section);
  };

  // Get the base path for GitHub Pages
  const isGitHubPages = window.location.hostname.includes('github.io');
  const basePath = isGitHubPages ? '/medikey' : '';

  const handleSectionClick = (section: string, e: React.MouseEvent) => {
    // Don't prevent default navigation - let the Link component handle it

    setShowConfetti(true);
    toast({
      title: `${section} Selected`,
      description: `You're now viewing the ${section} section`,
      variant: "default",
    });

    // Hide confetti after 2 seconds
    setTimeout(() => {
      setShowConfetti(false);
    }, 2000);
  };

  if (userLoading) {
    return <DashboardSkeleton />;
  }

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:via-blue-950 dark:to-purple-950 py-6 px-4 sm:px-6 lg:px-8 transition-all duration-500">
      {/* Confetti Effect */}
      <Confetti active={showConfetti} particleCount={150} duration={2000} />

      {/* Particle Background */}
      <ParticleBackground
        colorScheme={colorMode}
        particleCount={100}
        speed={0.4}
        interactive={true}
        density={1.2}
      />

      {/* Animated background glow effects */}
      <div className="fixed inset-0 -z-5 overflow-hidden opacity-70 dark:opacity-40">
        <div className="absolute top-1/4 -left-20 w-72 h-72 bg-blue-500/30 dark:bg-blue-500/20 rounded-full filter blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 -right-20 w-72 h-72 bg-purple-500/30 dark:bg-purple-500/20 rounded-full filter blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-pink-500/20 dark:bg-pink-500/10 rounded-full filter blur-3xl animate-pulse-slow" style={{ animationDelay: '2s' }}></div>
      </div>

      {/* Welcome Banner */}
      <div className={`transition-all duration-700 ease-in-out transform ${showWelcome ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-20 pointer-events-none absolute'}`}>
        <ColorfulCard
          className="mb-6 shadow-xl relative overflow-hidden"
          colorScheme="gradient-blue-purple"
          glowEffect={true}
          floatEffect={true}
          hoverAnimation="scale"
          entranceAnimation="fade-up"
        >
          {/* Animated background elements */}
          <div className="absolute inset-0 overflow-hidden">
            <div className="absolute top-0 left-0 w-20 h-20 bg-blue-500/10 dark:bg-blue-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '7s' }}></div>
            <div className="absolute bottom-0 right-0 w-32 h-32 bg-purple-500/10 dark:bg-purple-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '10s', animationDelay: '1s' }}></div>
          </div>

          <div className="p-6 bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 dark:from-blue-500/20 dark:via-indigo-500/20 dark:to-purple-500/20 backdrop-blur-sm rounded-lg relative z-10">
            <div className="flex items-center">
              <div className="relative">
                <div className="absolute inset-0 bg-yellow-400/30 dark:bg-yellow-400/20 rounded-full filter blur-md animate-pulse-slow"></div>
                <Sparkles className="h-7 w-7 text-yellow-500 dark:text-yellow-300 relative z-10 animate-pulse-glow" />
              </div>
              <div className="ml-4 flex-1">
                <h2 className="text-2xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-400 dark:to-purple-400 animate-gradient-shift">
                  Welcome back, {(userProfile as any)?.fullName || user?.username}!
                </h2>
                <div className="h-0.5 w-0 bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-400 dark:to-purple-400 mt-1 animate-expand"></div>
              </div>
            </div>

            <div className="mt-4 ml-11 space-y-3">
              <p className="text-base text-gray-700 dark:text-gray-200 animate-fadeIn" style={{ animationDelay: '0.3s' }}>
                Your health dashboard is ready. We've updated your metrics and records.
              </p>

              <div className="flex items-center space-x-2 animate-fadeIn" style={{ animationDelay: '0.6s' }}>
                <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse-slow"></div>
                <p className="text-sm text-gray-600 dark:text-gray-300">All systems are up to date</p>
              </div>

              <div className="flex items-center space-x-2 animate-fadeIn" style={{ animationDelay: '0.8s' }}>
                <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse-slow"></div>
                <p className="text-sm text-gray-600 dark:text-gray-300">3 new health insights available</p>
              </div>
            </div>

            <div className="mt-5 flex justify-end animate-fadeIn" style={{ animationDelay: '1s' }}>
              <ColorfulButton
                colorScheme="gradient-blue-purple"
                glowEffect={true}
                hoverAnimation="scale"
                className="relative overflow-hidden group"
                onClick={() => setShowWelcome(false)}
              >
                <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-blue-600/20 to-purple-600/20 dark:from-blue-400/20 dark:to-purple-400/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                <span className="relative flex items-center">
                  <Zap className="h-4 w-4 mr-2 text-yellow-300 animate-pulse-glow" />
                  Let's get started
                </span>
              </ColorfulButton>
            </div>
          </div>
        </ColorfulCard>
      </div>

      {/* Page Header */}
      <div className="mb-8 relative group">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-indigo-600/5 to-purple-600/5 dark:from-blue-400/10 dark:via-indigo-400/10 dark:to-purple-400/10 rounded-xl transform transition-all duration-300 group-hover:scale-105 group-hover:opacity-80"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-blue-600/5 via-indigo-600/5 to-purple-600/5 dark:from-blue-400/10 dark:via-indigo-400/10 dark:to-purple-400/10 rounded-xl blur-xl opacity-50 transform transition-all duration-300 group-hover:opacity-100"></div>

        <div className="relative bg-gradient-to-r from-blue-500/10 via-indigo-500/10 to-purple-500/10 dark:from-blue-500/20 dark:via-indigo-500/20 dark:to-purple-500/20 p-6 rounded-xl backdrop-blur-sm animate-fadeIn border border-white/10 dark:border-white/5 shadow-lg">
          <div className="flex items-start md:items-center">
            <div className="relative mr-4">
              <div className="absolute inset-0 bg-blue-500/30 dark:bg-blue-400/20 rounded-full filter blur-md animate-pulse-slow"></div>
              <Activity className="h-10 w-10 text-blue-600 dark:text-blue-300 relative z-10 animate-float" />
            </div>

            <div className="flex-1">
              <h1 className="text-3xl md:text-4xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-300 dark:to-purple-300 animate-gradient-shift">
                Health Dashboard
              </h1>
              <div className="h-1 w-0 bg-gradient-to-r from-blue-500 to-purple-600 dark:from-blue-400 dark:to-purple-400 mt-1 rounded-full animate-expand"></div>

              <p className="mt-3 text-base text-gray-700 dark:text-gray-200">
                Here's your personalized health overview for <span className="font-medium text-blue-700 dark:text-blue-300">{new Date().toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' })}</span>.
              </p>
            </div>
          </div>

          <div className="mt-4 flex flex-wrap gap-3">
            <div className="flex items-center space-x-2 bg-blue-500/10 dark:bg-blue-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.3s' }}>
              <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-blue-700 dark:text-blue-300">Health Score: 92/100</p>
            </div>

            <div className="flex items-center space-x-2 bg-green-500/10 dark:bg-green-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.5s' }}>
              <div className="w-2 h-2 rounded-full bg-green-500 dark:bg-green-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-green-700 dark:text-green-300">Last Updated: Today</p>
            </div>

            <div className="flex items-center space-x-2 bg-purple-500/10 dark:bg-purple-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.7s' }}>
              <div className="w-2 h-2 rounded-full bg-purple-500 dark:bg-purple-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-purple-700 dark:text-purple-300">3 New Insights</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Access Feature Navigation */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
        <Link href={`${basePath}/emergency`} onClick={(e) => handleSectionClick('Emergency Access', e)}>
          <InteractiveTooltip
            content={
              <div className="bg-gradient-to-r from-red-500/20 to-orange-500/20 dark:from-red-500/30 dark:to-orange-500/30 p-3 rounded-lg border border-red-200 dark:border-red-800">
                <p className="font-medium text-red-800 dark:text-red-200">Emergency Access</p>
                <p className="text-xs mt-1 text-red-700 dark:text-red-300">Quick access to critical health information for emergency situations</p>
              </div>
            }
            side="top"
            interactive
          >
            <div className={`relative transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '100ms' }}>
              {/* Glow effect */}
              <div className="absolute -inset-0.5 bg-gradient-to-r from-red-500 to-orange-500 dark:from-red-400 dark:to-orange-400 rounded-xl blur opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-gradient-shift"></div>

              <ColorfulCard
                className="relative p-1"
                colorScheme="gradient-orange-red"
                glowEffect={true}
                hoverAnimation="scale"
                entranceAnimation="fade-up"
                onMouseEnter={() => handleSectionHover('Emergency Access')}
              >
                {/* Animated background elements */}
                <div className="absolute inset-0 overflow-hidden rounded-lg">
                  <div className="absolute -top-4 -left-4 w-16 h-16 bg-red-500/10 dark:bg-red-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '7s' }}></div>
                  <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-orange-500/10 dark:bg-orange-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '9s', animationDelay: '1s' }}></div>
                </div>

                <div className="p-5 bg-gradient-to-br from-red-100/80 to-orange-50/60 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm rounded-lg flex flex-col items-center text-center relative z-10">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-red-500/30 to-orange-500/30 dark:from-red-400/30 dark:to-orange-400/30 rounded-full blur-md animate-pulse-slow"></div>
                    <div className="h-16 w-16 rounded-full bg-gradient-to-br from-red-500 to-orange-500 dark:from-red-400 dark:to-orange-400 flex items-center justify-center mb-3 shadow-lg shadow-red-500/20 animate-pulse-slow relative z-10">
                      <Shield className="h-8 w-8 text-white" />
                    </div>
                  </div>

                  <h3 className="text-base font-bold text-transparent bg-clip-text bg-gradient-to-br from-red-600 to-orange-600 dark:from-red-300 dark:to-orange-300 animate-gradient-shift">Emergency Access</h3>

                  <div className="h-0.5 w-0 bg-gradient-to-r from-red-500 to-orange-500 dark:from-red-400 dark:to-orange-400 mt-1 rounded-full group-hover:animate-expand"></div>

                  <p className="text-sm text-red-700 dark:text-red-300 mt-2">Critical health info</p>

                  <div className="absolute bottom-2 right-2 w-2 h-2 rounded-full bg-red-500 dark:bg-red-400 opacity-70 animate-pulse-slow"></div>
                </div>
              </ColorfulCard>
            </div>
          </InteractiveTooltip>
        </Link>

        <Link href={`${basePath}/records`} onClick={(e) => handleSectionClick('Medical Records', e)}>
          <div className={`relative transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '200ms' }}>
            {/* Glow effect */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-blue-500 to-purple-500 dark:from-blue-400 dark:to-purple-400 rounded-xl blur opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-gradient-shift"></div>

            <ColorfulCard
              className="relative p-1"
              colorScheme="gradient-blue-purple"
              glowEffect={true}
              hoverAnimation="lift"
              entranceAnimation="fade-up"
              onMouseEnter={() => handleSectionHover('Medical Records')}
            >
              {/* Animated background elements */}
              <div className="absolute inset-0 overflow-hidden rounded-lg">
                <div className="absolute -top-4 -left-4 w-16 h-16 bg-blue-500/10 dark:bg-blue-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '8s' }}></div>
                <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-purple-500/10 dark:bg-purple-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '10s', animationDelay: '1s' }}></div>
              </div>

              <div className="p-5 bg-gradient-to-br from-blue-100/80 to-purple-50/60 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm rounded-lg flex flex-col items-center text-center relative z-10">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/30 to-purple-500/30 dark:from-blue-400/30 dark:to-purple-400/30 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="h-16 w-16 rounded-full bg-gradient-to-br from-blue-500 to-purple-500 dark:from-blue-400 dark:to-purple-400 flex items-center justify-center mb-3 shadow-lg shadow-blue-500/20 animate-float relative z-10">
                    <FileText className="h-8 w-8 text-white" />
                  </div>
                </div>

                <h3 className="text-base font-bold text-transparent bg-clip-text bg-gradient-to-br from-blue-600 to-purple-600 dark:from-blue-300 dark:to-purple-300 animate-gradient-shift">Medical Records</h3>

                <div className="h-0.5 w-0 bg-gradient-to-r from-blue-500 to-purple-500 dark:from-blue-400 dark:to-purple-400 mt-1 rounded-full group-hover:animate-expand"></div>

                <p className="text-sm text-blue-700 dark:text-blue-300 mt-2">View & manage records</p>

                <div className="absolute bottom-2 right-2 w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 opacity-70 animate-pulse-slow"></div>
              </div>
            </ColorfulCard>
          </div>
        </Link>

        <Link href={`${basePath}/appointments`} onClick={(e) => handleSectionClick('Appointments', e)}>
          <div className={`relative transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '300ms' }}>
            {/* Glow effect */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-green-500 to-blue-500 dark:from-green-400 dark:to-blue-400 rounded-xl blur opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-gradient-shift"></div>

            <ColorfulCard
              className="relative p-1"
              colorScheme="gradient-green-blue"
              glowEffect={true}
              hoverAnimation="lift"
              entranceAnimation="fade-up"
              onMouseEnter={() => handleSectionHover('Appointments')}
            >
              {/* Animated background elements */}
              <div className="absolute inset-0 overflow-hidden rounded-lg">
                <div className="absolute -top-4 -left-4 w-16 h-16 bg-green-500/10 dark:bg-green-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '9s' }}></div>
                <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-blue-500/10 dark:bg-blue-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '11s', animationDelay: '1s' }}></div>
              </div>

              <div className="p-5 bg-gradient-to-br from-green-100/80 to-blue-50/60 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm rounded-lg flex flex-col items-center text-center relative z-10">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-green-500/30 to-blue-500/30 dark:from-green-400/30 dark:to-blue-400/30 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="h-16 w-16 rounded-full bg-gradient-to-br from-green-500 to-blue-500 dark:from-green-400 dark:to-blue-400 flex items-center justify-center mb-3 shadow-lg shadow-green-500/20 animate-float relative z-10">
                    <Calendar className="h-8 w-8 text-white" />
                  </div>
                </div>

                <h3 className="text-base font-bold text-transparent bg-clip-text bg-gradient-to-br from-green-600 to-blue-600 dark:from-green-300 dark:to-blue-300 animate-gradient-shift">Appointments</h3>

                <div className="h-0.5 w-0 bg-gradient-to-r from-green-500 to-blue-500 dark:from-green-400 dark:to-blue-400 mt-1 rounded-full group-hover:animate-expand"></div>

                <p className="text-sm text-green-700 dark:text-green-300 mt-2">Schedule & track visits</p>

                <div className="absolute bottom-2 right-2 w-2 h-2 rounded-full bg-green-500 dark:bg-green-400 opacity-70 animate-pulse-slow"></div>
              </div>
            </ColorfulCard>
          </div>
        </Link>

        <Link href={`${basePath}/analytics`} onClick={(e) => handleSectionClick('Health Analytics', e)}>
          <div className={`relative transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '400ms' }}>
            {/* Glow effect */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-pink-500 to-purple-500 dark:from-pink-400 dark:to-purple-400 rounded-xl blur opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-gradient-shift"></div>

            <ColorfulCard
              className="relative p-1"
              colorScheme="gradient-pink-purple"
              glowEffect={true}
              hoverAnimation="lift"
              entranceAnimation="fade-up"
              onMouseEnter={() => handleSectionHover('Health Analytics')}
            >
              {/* Animated background elements */}
              <div className="absolute inset-0 overflow-hidden rounded-lg">
                <div className="absolute -top-4 -left-4 w-16 h-16 bg-pink-500/10 dark:bg-pink-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '8.5s' }}></div>
                <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-purple-500/10 dark:bg-purple-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '10.5s', animationDelay: '1s' }}></div>
              </div>

              <div className="p-5 bg-gradient-to-br from-pink-100/80 to-purple-50/60 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm rounded-lg flex flex-col items-center text-center relative z-10">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-pink-500/30 to-purple-500/30 dark:from-pink-400/30 dark:to-purple-400/30 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="h-16 w-16 rounded-full bg-gradient-to-br from-pink-500 to-purple-500 dark:from-pink-400 dark:to-purple-400 flex items-center justify-center mb-3 shadow-lg shadow-pink-500/20 animate-pulse-slow relative z-10">
                    <Heart className="h-8 w-8 text-white" />
                  </div>
                </div>

                <h3 className="text-base font-bold text-transparent bg-clip-text bg-gradient-to-br from-pink-600 to-purple-600 dark:from-pink-300 dark:to-purple-300 animate-gradient-shift">Health Analytics</h3>

                <div className="h-0.5 w-0 bg-gradient-to-r from-pink-500 to-purple-500 dark:from-pink-400 dark:to-purple-400 mt-1 rounded-full group-hover:animate-expand"></div>

                <p className="text-sm text-pink-700 dark:text-pink-300 mt-2">Track your metrics</p>

                <div className="absolute bottom-2 right-2 w-2 h-2 rounded-full bg-pink-500 dark:bg-pink-400 opacity-70 animate-pulse-slow"></div>
              </div>
            </ColorfulCard>
          </div>
        </Link>

        <Link href={`${basePath}/family`} onClick={(e) => handleSectionClick('Family Vault', e)}>
          <div className={`relative transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '500ms' }}>
            {/* Glow effect */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-cyan-500 to-blue-500 dark:from-cyan-400 dark:to-blue-400 rounded-xl blur opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-gradient-shift"></div>

            <ColorfulCard
              className="relative p-1"
              colorScheme="gradient-cyan-blue"
              glowEffect={true}
              hoverAnimation="lift"
              entranceAnimation="fade-up"
              onMouseEnter={() => handleSectionHover('Family Vault')}
            >
              {/* Animated background elements */}
              <div className="absolute inset-0 overflow-hidden rounded-lg">
                <div className="absolute -top-4 -left-4 w-16 h-16 bg-cyan-500/10 dark:bg-cyan-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '7.5s' }}></div>
                <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-blue-500/10 dark:bg-blue-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '9.5s', animationDelay: '1s' }}></div>
              </div>

              <div className="p-5 bg-gradient-to-br from-cyan-100/80 to-blue-50/60 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm rounded-lg flex flex-col items-center text-center relative z-10">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/30 to-blue-500/30 dark:from-cyan-400/30 dark:to-blue-400/30 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="h-16 w-16 rounded-full bg-gradient-to-br from-cyan-500 to-blue-500 dark:from-cyan-400 dark:to-blue-400 flex items-center justify-center mb-3 shadow-lg shadow-cyan-500/20 animate-float relative z-10">
                    <Users className="h-8 w-8 text-white" />
                  </div>
                </div>

                <h3 className="text-base font-bold text-transparent bg-clip-text bg-gradient-to-br from-cyan-600 to-blue-600 dark:from-cyan-300 dark:to-blue-300 animate-gradient-shift">Family Vault</h3>

                <div className="h-0.5 w-0 bg-gradient-to-r from-cyan-500 to-blue-500 dark:from-cyan-400 dark:to-blue-400 mt-1 rounded-full group-hover:animate-expand"></div>

                <p className="text-sm text-cyan-700 dark:text-cyan-300 mt-2">Manage dependents</p>

                <div className="absolute bottom-2 right-2 w-2 h-2 rounded-full bg-cyan-500 dark:bg-cyan-400 opacity-70 animate-pulse-slow"></div>
              </div>
            </ColorfulCard>
          </div>
        </Link>

        <Link href={`${basePath}/assistant`} onClick={(e) => handleSectionClick('AI Assistant', e)}>
          <div className={`relative transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '600ms' }}>
            {/* Glow effect */}
            <div className="absolute -inset-0.5 bg-gradient-to-r from-yellow-500 to-orange-500 dark:from-yellow-400 dark:to-orange-400 rounded-xl blur opacity-30 group-hover:opacity-100 transition duration-1000 group-hover:duration-200 animate-gradient-shift"></div>

            <ColorfulCard
              className="relative p-1"
              colorScheme="gradient-yellow-orange"
              glowEffect={true}
              hoverAnimation="lift"
              entranceAnimation="fade-up"
              onMouseEnter={() => handleSectionHover('AI Assistant')}
            >
              {/* Animated background elements */}
              <div className="absolute inset-0 overflow-hidden rounded-lg">
                <div className="absolute -top-4 -left-4 w-16 h-16 bg-yellow-500/10 dark:bg-yellow-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '8s' }}></div>
                <div className="absolute -bottom-4 -right-4 w-16 h-16 bg-orange-500/10 dark:bg-orange-400/10 rounded-full filter blur-xl animate-float" style={{ animationDuration: '10s', animationDelay: '1s' }}></div>
              </div>

              <div className="p-5 bg-gradient-to-br from-yellow-100/80 to-orange-50/60 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm rounded-lg flex flex-col items-center text-center relative z-10">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-yellow-500/30 to-orange-500/30 dark:from-yellow-400/30 dark:to-orange-400/30 rounded-full blur-md animate-pulse-slow"></div>
                  <div className="h-16 w-16 rounded-full bg-gradient-to-br from-yellow-500 to-orange-500 dark:from-yellow-400 dark:to-orange-400 flex items-center justify-center mb-3 shadow-lg shadow-yellow-500/20 animate-pulse-slow relative z-10">
                    <Bot className="h-8 w-8 text-white" />
                  </div>
                </div>

                <h3 className="text-base font-bold text-transparent bg-clip-text bg-gradient-to-br from-yellow-600 to-orange-600 dark:from-yellow-300 dark:to-orange-300 animate-gradient-shift">AI Assistant</h3>

                <div className="h-0.5 w-0 bg-gradient-to-r from-yellow-500 to-orange-500 dark:from-yellow-400 dark:to-orange-400 mt-1 rounded-full group-hover:animate-expand"></div>

                <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-2">Get health answers</p>

                <div className="absolute bottom-2 right-2 w-2 h-2 rounded-full bg-yellow-500 dark:bg-yellow-400 opacity-70 animate-pulse-slow"></div>
              </div>
            </ColorfulCard>
          </div>
        </Link>
      </div>



      {/* Emergency Information Card */}
      <div className="mb-8 animate-fadeIn stagger-1">
        <ColorfulCard
          colorScheme="gradient-orange-red"
          glowEffect={true}
          hoverAnimation="lift"
          entranceAnimation="fade-up"
          className="overflow-hidden"
        >
          <div className="bg-gradient-to-r from-red-500/10 to-orange-500/10 dark:from-red-500/20 dark:to-orange-500/20 p-2 border-b border-red-200 dark:border-red-800">
            <h2 className="text-xl font-bold text-gradient text-orange-gradient flex items-center pl-2">
              <Shield className="h-5 w-5 mr-2 text-red-600 dark:text-red-400 animate-pulse-slow" />
              Emergency Information
            </h2>
          </div>
          <div className="p-4 bg-gradient-to-br from-red-100/50 to-orange-50/30 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm">
            <EmergencyInfoCard userProfile={userProfile as any} />
          </div>
        </ColorfulCard>
      </div>

      {/* Dashboard Content */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3 mb-8">
        {/* Medical Summary Card */}
        <div className="lg:col-span-2 animate-fadeIn stagger-2">
          <ColorfulCard
            colorScheme="gradient-blue-purple"
            glowEffect={true}
            hoverAnimation="lift"
            entranceAnimation="fade-up"
            className="overflow-hidden"
          >
            <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 p-2 border-b border-blue-200 dark:border-blue-800">
              <h2 className="text-xl font-bold text-gradient text-blue-gradient flex items-center pl-2">
                <Activity className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 animate-pulse-slow" />
                Medical Summary
              </h2>
            </div>
            <div className="p-4 bg-gradient-to-br from-blue-100/50 to-purple-50/30 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm">
              <MedicalSummaryCard className="border-0 shadow-none" />
            </div>
          </ColorfulCard>
        </div>

        {/* Upcoming Appointments Card */}
        <div className="animate-fadeIn stagger-3">
          <ColorfulCard
            colorScheme="gradient-green-blue"
            glowEffect={true}
            hoverAnimation="lift"
            entranceAnimation="fade-up"
            className="overflow-hidden"
          >
            <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 dark:from-green-500/20 dark:to-blue-500/20 p-2 border-b border-green-200 dark:border-green-800">
              <h2 className="text-xl font-bold text-gradient text-green-gradient flex items-center pl-2">
                <Calendar className="h-5 w-5 mr-2 text-green-600 dark:text-green-400 animate-pulse-slow" />
                Upcoming Appointments
              </h2>
            </div>
            <div className="p-4 bg-gradient-to-br from-green-100/50 to-blue-50/30 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm">
              <div className="border-0 shadow-none">
                <AppointmentsCard />
              </div>
            </div>
          </ColorfulCard>
        </div>
      </div>

      {/* Health Analytics Section */}
      <div className="mb-8 animate-fadeIn stagger-4">
        <ColorfulCard
          colorScheme="gradient-pink-purple"
          glowEffect={true}
          hoverAnimation="lift"
          entranceAnimation="fade-up"
          className="overflow-hidden"
        >
          <div className="bg-gradient-to-r from-pink-500/10 to-purple-500/10 dark:from-pink-500/20 dark:to-purple-500/20 p-2 border-b border-pink-200 dark:border-pink-800">
            <h2 className="text-xl font-bold text-gradient text-pink-gradient flex items-center pl-2">
              <Heart className="h-5 w-5 mr-2 text-pink-600 dark:text-pink-400 animate-pulse-slow" />
              Health Analytics
            </h2>
          </div>
          <div className="p-4 bg-gradient-to-br from-pink-100/50 to-purple-50/30 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm">
            <div className="border-0 shadow-none">
              <HealthAnalyticsSection />
            </div>
          </div>
        </ColorfulCard>
      </div>

      {/* Recent Medical Records Section */}
      <div className="mb-8 animate-fadeIn stagger-5">
        <ColorfulCard
          colorScheme="gradient-cyan-blue"
          glowEffect={true}
          hoverAnimation="lift"
          entranceAnimation="fade-up"
          className="overflow-hidden"
        >
          <div className="bg-gradient-to-r from-cyan-600/20 to-blue-600/20 dark:from-cyan-500/30 dark:to-blue-500/30 p-3 border-b border-cyan-200 dark:border-cyan-700">
            <h2 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-cyan-700 to-blue-700 dark:from-cyan-300 dark:to-blue-300 flex items-center pl-2">
              <div className="relative mr-2">
                <div className="absolute inset-0 bg-cyan-400/20 dark:bg-cyan-400/30 rounded-full blur-sm animate-pulse-slow"></div>
                <FileText className="h-5 w-5 text-cyan-700 dark:text-cyan-300 relative z-10" />
              </div>
              Recent Medical Records
            </h2>
          </div>
          <div className="p-5 bg-gradient-to-br from-cyan-100/70 to-blue-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm">
            <div className="border-0 shadow-none text-gray-800 dark:text-gray-100">
              <RecentMedicalRecordsSection />
            </div>
          </div>
        </ColorfulCard>
      </div>

      {/* Family Vault Section */}
      <div className="mb-8 animate-fadeIn stagger-6">
        <ColorfulCard
          colorScheme="gradient-blue-purple"
          glowEffect={true}
          hoverAnimation="lift"
          entranceAnimation="fade-up"
          className="overflow-hidden"
        >
          <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 p-2 border-b border-blue-200 dark:border-blue-800">
            <h2 className="text-xl font-bold text-gradient text-blue-gradient flex items-center pl-2">
              <Users className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400 animate-pulse-slow" />
              Family Vault
            </h2>
          </div>
          <div className="p-4 bg-gradient-to-br from-blue-100/50 to-purple-50/30 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm">
            <div className="border-0 shadow-none">
              <FamilyVaultSection />
            </div>
          </div>
        </ColorfulCard>
      </div>

      {/* Smartwatch Integration */}
      <div className="mb-8 animate-fadeIn" style={{ animationDelay: '700ms' }}>
        <ColorfulCard
          colorScheme="gradient-green-blue"
          glowEffect={true}
          hoverAnimation="lift"
          entranceAnimation="fade-up"
          className="overflow-hidden"
        >
          <div className="bg-gradient-to-r from-green-500/10 to-blue-500/10 dark:from-green-500/20 dark:to-blue-500/20 p-2 border-b border-green-200 dark:border-green-800">
            <h2 className="text-xl font-bold text-gradient text-green-gradient flex items-center pl-2">
              <Watch className="h-5 w-5 mr-2 text-green-600 dark:text-green-400 animate-pulse-slow" />
              Smartwatch Integration
            </h2>
          </div>
          <div className="p-4 bg-gradient-to-br from-green-100/50 to-blue-50/30 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm">
            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
              <LiveHealthMetrics />
              <SmartWatchIntegration userId={user?.id || 0} />
            </div>
          </div>
        </ColorfulCard>
      </div>

      {/* AI Assistant Card */}
      <div className="mb-8 animate-fadeIn" style={{ animationDelay: '800ms' }}>
        <ColorfulCard
          colorScheme="gradient-yellow-orange"
          glowEffect={true}
          hoverAnimation="lift"
          entranceAnimation="fade-up"
          className="overflow-hidden"
        >
          <div className="bg-gradient-to-r from-yellow-600/20 to-orange-600/20 dark:from-yellow-500/30 dark:to-orange-500/30 p-3 border-b border-yellow-200 dark:border-yellow-700">
            <h2 className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-yellow-700 to-orange-700 dark:from-yellow-300 dark:to-orange-300 flex items-center pl-2">
              <div className="relative mr-2">
                <div className="absolute inset-0 bg-yellow-400/20 dark:bg-yellow-400/30 rounded-full blur-sm animate-pulse-slow"></div>
                <Bot className="h-5 w-5 text-yellow-700 dark:text-yellow-300 relative z-10" />
              </div>
              AI Health Assistant
            </h2>
          </div>
          <div className="p-5 bg-gradient-to-br from-yellow-100/70 to-orange-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm">
            <div className="border-0 shadow-none text-gray-800 dark:text-gray-100">
              <AIAssistantCard />
            </div>
          </div>
        </ColorfulCard>
      </div>
    </div>
  );
}

function DashboardSkeleton() {
  return (
    <div className="min-h-screen relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-blue-950 py-6 px-4 sm:px-6 lg:px-8">
      {/* Particle Background Skeleton */}
      <div className="fixed inset-0 -z-10 opacity-30">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="absolute rounded-full bg-blue-400 dark:bg-blue-600 animate-pulse-slow"
            style={{
              width: `${Math.random() * 10 + 5}px`,
              height: `${Math.random() * 10 + 5}px`,
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              opacity: Math.random() * 0.5 + 0.2
            }}
          />
        ))}
      </div>

      {/* Header Skeleton */}
      <div className="mb-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 dark:from-blue-500/20 dark:to-purple-500/20 p-4 rounded-xl backdrop-blur-sm">
        <Skeleton className="h-10 w-64 mb-2 bg-blue-200/50 dark:bg-blue-700/50 rounded-lg" />
        <Skeleton className="h-5 w-96 bg-blue-100/50 dark:bg-blue-800/50 rounded-lg" />
      </div>

      {/* Quick Access Feature Navigation Skeleton */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-8">
        {[...Array(6)].map((_, index) => (
          <div key={index} className="p-1 rounded-lg bg-gradient-to-br from-blue-500/20 to-purple-500/20 dark:from-blue-500/30 dark:to-purple-500/30 animate-pulse-slow" style={{ animationDelay: `${index * 0.1}s` }}>
            <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-4">
              <div className="flex flex-col items-center">
                <Skeleton className="h-14 w-14 rounded-full mb-3 bg-blue-200/50 dark:bg-blue-700/50" />
                <Skeleton className="h-5 w-28 mb-2 bg-blue-100/50 dark:bg-blue-800/50 rounded-lg" />
                <Skeleton className="h-4 w-20 bg-blue-100/30 dark:bg-blue-800/30 rounded-lg" />
              </div>
            </div>
          </div>
        ))}
      </div>



      {/* Emergency Info Card Skeleton */}
      <div className="mb-8">
        <div className="p-1 rounded-lg bg-gradient-to-br from-red-500/20 to-orange-500/20 dark:from-red-500/30 dark:to-orange-500/30 animate-pulse-slow">
          <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-4">
            <Skeleton className="h-8 w-64 mb-4 bg-red-200/50 dark:bg-red-700/50 rounded-lg" />
            <Skeleton className="h-40 w-full bg-red-100/30 dark:bg-red-800/30 rounded-lg" />
          </div>
        </div>
      </div>

      {/* Dashboard Content Skeleton */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3 mb-8">
        <div className="lg:col-span-2">
          <div className="p-1 rounded-lg bg-gradient-to-br from-blue-500/20 to-purple-500/20 dark:from-blue-500/30 dark:to-purple-500/30 animate-pulse-slow">
            <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-4">
              <Skeleton className="h-8 w-64 mb-4 bg-blue-200/50 dark:bg-blue-700/50 rounded-lg" />
              <Skeleton className="h-72 w-full bg-blue-100/30 dark:bg-blue-800/30 rounded-lg" />
            </div>
          </div>
        </div>

        <div>
          <div className="p-1 rounded-lg bg-gradient-to-br from-green-500/20 to-blue-500/20 dark:from-green-500/30 dark:to-blue-500/30 animate-pulse-slow" style={{ animationDelay: '0.2s' }}>
            <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-4">
              <Skeleton className="h-8 w-64 mb-4 bg-green-200/50 dark:bg-green-700/50 rounded-lg" />
              <Skeleton className="h-72 w-full bg-green-100/30 dark:bg-green-800/30 rounded-lg" />
            </div>
          </div>
        </div>
      </div>

      {/* Additional Sections Skeletons */}
      {[...Array(4)].map((_, index) => (
        <div key={index} className="mb-8">
          <div
            className={`p-1 rounded-lg animate-pulse-slow`}
            style={{
              animationDelay: `${0.3 + index * 0.1}s`,
              background: index % 4 === 0 ? 'linear-gradient(135deg, rgba(236, 72, 153, 0.2), rgba(139, 92, 246, 0.2))' :
                          index % 4 === 1 ? 'linear-gradient(135deg, rgba(6, 182, 212, 0.2), rgba(59, 130, 246, 0.2))' :
                          index % 4 === 2 ? 'linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(59, 130, 246, 0.2))' :
                                          'linear-gradient(135deg, rgba(250, 204, 21, 0.2), rgba(249, 115, 22, 0.2))'
            }}
          >
            <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-4">
              <Skeleton
                className={`h-8 w-64 mb-4 rounded-lg`}
                style={{
                  background: index % 4 === 0 ? 'rgba(236, 72, 153, 0.2)' :
                              index % 4 === 1 ? 'rgba(6, 182, 212, 0.2)' :
                              index % 4 === 2 ? 'rgba(16, 185, 129, 0.2)' :
                                              'rgba(250, 204, 21, 0.2)'
                }}
              />
              <Skeleton
                className={`h-64 w-full rounded-lg`}
                style={{
                  background: index % 4 === 0 ? 'rgba(236, 72, 153, 0.1)' :
                              index % 4 === 1 ? 'rgba(6, 182, 212, 0.1)' :
                              index % 4 === 2 ? 'rgba(16, 185, 129, 0.1)' :
                                              'rgba(250, 204, 21, 0.1)'
                }}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

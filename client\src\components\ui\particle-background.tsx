import React, { useEffect, useRef } from 'react';

interface Particle {
  x: number;
  y: number;
  size: number;
  speedX: number;
  speedY: number;
  color: string;
}

interface ParticleBackgroundProps {
  className?: string;
  particleCount?: number;
  colorScheme?: 'blue' | 'purple' | 'rainbow' | 'green' | 'pink' | 'orange';
  speed?: number;
  interactive?: boolean;
  density?: number;
}

export function ParticleBackground({
  className = '',
  particleCount = 50,
  colorScheme = 'blue',
  speed = 1,
  interactive = true,
  density = 1,
}: ParticleBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const particlesRef = useRef<Particle[]>([]);
  const mouseRef = useRef({ x: 0, y: 0, radius: 100 });
  const animationFrameRef = useRef<number>(0);

  const getColor = () => {
    switch (colorScheme) {
      case 'blue':
        return `rgba(59, 130, 246, ${Math.random() * 0.5 + 0.2})`;
      case 'purple':
        return `rgba(139, 92, 246, ${Math.random() * 0.5 + 0.2})`;
      case 'green':
        return `rgba(16, 185, 129, ${Math.random() * 0.5 + 0.2})`;
      case 'pink':
        return `rgba(236, 72, 153, ${Math.random() * 0.5 + 0.2})`;
      case 'orange':
        return `rgba(249, 115, 22, ${Math.random() * 0.5 + 0.2})`;
      case 'rainbow':
        const hue = Math.floor(Math.random() * 360);
        return `hsla(${hue}, 70%, 60%, ${Math.random() * 0.5 + 0.2})`;
      default:
        return `rgba(59, 130, 246, ${Math.random() * 0.5 + 0.2})`;
    }
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const handleResize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
      initParticles();
    };

    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current.x = e.clientX;
      mouseRef.current.y = e.clientY;
    };

    const initParticles = () => {
      particlesRef.current = [];
      const actualCount = Math.floor(particleCount * density);
      
      for (let i = 0; i < actualCount; i++) {
        particlesRef.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          size: Math.random() * 3 + 1,
          speedX: (Math.random() - 0.5) * speed,
          speedY: (Math.random() - 0.5) * speed,
          color: getColor(),
        });
      }
    };

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particlesRef.current.forEach((particle) => {
        // Update position
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        
        // Bounce off edges
        if (particle.x > canvas.width || particle.x < 0) {
          particle.speedX = -particle.speedX;
        }
        if (particle.y > canvas.height || particle.y < 0) {
          particle.speedY = -particle.speedY;
        }
        
        // Mouse interaction
        if (interactive) {
          const dx = particle.x - mouseRef.current.x;
          const dy = particle.y - mouseRef.current.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < mouseRef.current.radius) {
            const angle = Math.atan2(dy, dx);
            const force = (mouseRef.current.radius - distance) / mouseRef.current.radius;
            
            particle.x += Math.cos(angle) * force * 2;
            particle.y += Math.sin(angle) * force * 2;
          }
        }
        
        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();
        
        // Connect particles
        connectParticles(particle);
      });
      
      animationFrameRef.current = requestAnimationFrame(animate);
    };
    
    const connectParticles = (particle: Particle) => {
      particlesRef.current.forEach((otherParticle) => {
        if (particle === otherParticle) return;
        
        const dx = particle.x - otherParticle.x;
        const dy = particle.y - otherParticle.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        
        if (distance < 100) {
          ctx.beginPath();
          ctx.strokeStyle = particle.color.replace(/[^,]+(?=\))/, (Math.random() * 0.15).toString());
          ctx.lineWidth = 0.5;
          ctx.moveTo(particle.x, particle.y);
          ctx.lineTo(otherParticle.x, otherParticle.y);
          ctx.stroke();
        }
      });
    };

    window.addEventListener('resize', handleResize);
    if (interactive) {
      window.addEventListener('mousemove', handleMouseMove);
    }
    
    handleResize();
    animate();
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (interactive) {
        window.removeEventListener('mousemove', handleMouseMove);
      }
      cancelAnimationFrame(animationFrameRef.current);
    };
  }, [particleCount, colorScheme, speed, interactive, density]);

  return (
    <canvas
      ref={canvasRef}
      className={`fixed inset-0 -z-10 ${className}`}
    />
  );
}

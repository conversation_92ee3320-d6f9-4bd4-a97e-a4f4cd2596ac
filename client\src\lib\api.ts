import { apiRequest } from "./queryClient";
import config from "../config";
import { mockApi, shouldUseMockApi } from "./mockApi";

// Check if we should use the mock API (for GitHub Pages)
const useMockApi = shouldUseMockApi();

/**
 * Authentication
 */
export const loginUser = async (username: string, password: string) => {
  if (useMockApi) {
    return mockApi.auth.login({ username, password });
  }
  const res = await apiRequest("POST", "/api/auth/login", { username, password });
  return res.json();
};

export const registerUser = async (userData: any) => {
  if (useMockApi) {
    return mockApi.auth.register(userData);
  }
  const res = await apiRequest("POST", "/api/auth/register", userData);
  return res.json();
};

export const logoutUser = async () => {
  if (useMockApi) {
    return mockApi.auth.logout();
  }
  const res = await apiRequest("POST", "/api/auth/logout", {});
  return res.json();
};

export const getCurrentUser = async () => {
  if (useMockApi) {
    return mockApi.auth.me();
  }
  const res = await apiRequest("GET", "/api/auth/me", undefined);
  return res.json();
};

/**
 * Medical Records
 */
export const uploadMedicalRecord = async (formData: FormData) => {
  if (useMockApi) {
    return mockApi.records.upload(formData);
  }

  // Note: apiRequest doesn't handle FormData, so using fetch directly
  // Use the correct API base URL from config
  const fullUrl = `${config.apiBaseUrl}/records`;

  const res = await fetch(fullUrl, {
    method: "POST",
    body: formData,
    credentials: "include",
  });

  if (!res.ok) {
    const text = await res.text();
    throw new Error(`${res.status}: ${text || res.statusText}`);
  }

  return res;
};

export const deleteMedicalRecord = async (recordId: number) => {
  if (useMockApi) {
    return mockApi.records.delete(recordId.toString());
  }
  return apiRequest("DELETE", `/api/records/${recordId}`, undefined);
};

export const generateAISummary = async (recordId: number) => {
  // Mock API doesn't have this endpoint, but we'll use the real API endpoint
  return apiRequest("POST", `/api/records/${recordId}/generate-summary`, undefined);
};

/**
 * Health Metrics
 */
export const addHealthMetric = async (data: any) => {
  if (useMockApi) {
    return mockApi.healthMetrics.add(data);
  }
  return apiRequest("POST", "/api/health-metrics", data);
};

/**
 * Appointments
 */
export const createAppointment = async (data: any) => {
  if (useMockApi) {
    return mockApi.appointments.create(data);
  }
  return apiRequest("POST", "/api/appointments", data);
};

export const updateAppointmentStatus = async (appointmentId: number, status: string) => {
  if (useMockApi) {
    return mockApi.appointments.updateStatus(appointmentId.toString(), status);
  }
  return apiRequest("PATCH", `/api/appointments/${appointmentId}/status`, { status });
};

/**
 * Family Members
 */
export const addFamilyMember = async (data: any) => {
  if (useMockApi) {
    return mockApi.family.addMember(data);
  }
  return apiRequest("POST", "/api/family-members", data);
};

export const updateFamilyMember = async (memberId: number, data: any) => {
  if (useMockApi) {
    return mockApi.family.updateMember(memberId.toString(), data);
  }
  return apiRequest("PATCH", `/api/family-members/${memberId}`, data);
};

export const deleteFamilyMember = async (memberId: number) => {
  if (useMockApi) {
    return mockApi.family.deleteMember(memberId.toString());
  }
  return apiRequest("DELETE", `/api/family-members/${memberId}`, undefined);
};

/**
 * AI Assistant
 */
export const sendMessageToAI = async (message: string) => {
  if (useMockApi) {
    return mockApi.aiAssistant.sendMessage(message);
  }
  return apiRequest("POST", "/api/ai-chat", { message });
};

/**
 * User Profile
 */
export const updateUserProfile = async (data: any) => {
  if (useMockApi) {
    return mockApi.profile.update(data);
  }
  return apiRequest("PATCH", "/api/users/profile", data);
};

export const updateProfileAvatar = async (avatarUrl: string) => {
  if (useMockApi) {
    return mockApi.profile.updateAvatar(avatarUrl);
  }
  return apiRequest("PATCH", "/api/users/profile", { avatarUrl });
};

export const updateEmergencyInfo = async (data: any) => {
  if (useMockApi) {
    return mockApi.emergency.updateInfo(data);
  }
  return apiRequest("PATCH", "/api/users/emergency-info", data);
};

/**
 * Emergency Access
 */
export const getEmergencyAccessQR = async () => {
  if (useMockApi) {
    return mockApi.emergency.getQRCode();
  }
  const res = await apiRequest("GET", "/api/emergency/qr-code", undefined);
  return res.json();
};

/**
 * Smartwatch Integration
 */
export const getConnectedSmartWatchDevices = async () => {
  if (useMockApi) {
    return mockApi.smartwatch.getConnectedDevices();
  }
  const res = await apiRequest("GET", "/api/smartwatch/connected-devices", undefined);
  return res.json();
};

export const sendCommandToSmartWatch = async (deviceId: string, command: string, data: any = {}) => {
  if (useMockApi) {
    return mockApi.smartwatch.sendCommand(deviceId, command, data);
  }
  return apiRequest("POST", "/api/smartwatch/send-command", { deviceId, command, data });
};

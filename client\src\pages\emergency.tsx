"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle
} from "@/components/ui/card";
import { useQuery } from "@tanstack/react-query";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON><PERSON><PERSON>riangle, QrCode, Shield, Clock, Printer, Share2, Copy, Sparkles, Activity } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from "@/components/ui/dialog";
import QRCodeSVG from "react-qr-code";
import { useAuth } from "@/hooks/useAuth";
import { getEmergencyAccessQR } from "@/lib/api";
import { EmergencyQRCode } from "@/lib/types";

import { InteractiveTooltip } from "@/components/ui/interactive-tooltip";
import { useToast } from "@/hooks/use-toast";
import { ColorfulButton } from "@/components/ui/colorful-button";
import { ColorfulCard } from "@/components/ui/colorful-card";
import { ParticleBackground } from "@/components/ui/particle-background";

export default function Emergency() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [showQrCode, setShowQrCode] = useState(false);
  const [animateIn, setAnimateIn] = useState(false);
  const [countdown, setCountdown] = useState(24); // 24 hours countdown
  const [copied, setCopied] = useState(false);

  const { data: qrCode, isLoading: qrLoading } = useQuery<EmergencyQRCode>({
    queryKey: ["emergency-qr-code"],
    queryFn: getEmergencyAccessQR,
  });

  const { data: userProfile, isLoading: profileLoading } = useQuery({
    queryKey: ["user-profile"],
    queryFn: async () => {
      const res = await fetch("/api/users/profile");
      if (!res.ok) throw new Error("Failed to fetch user profile");
      return res.json();
    }
  });

  useEffect(() => {
    // Animate in elements
    const timer = setTimeout(() => {
      setAnimateIn(true);
    }, 100);

    // Simulate countdown timer
    const interval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 0) return 24;
        return prev - 0.01;
      });
    }, 36000); // Update roughly every minute in our simulation

    return () => {
      clearTimeout(timer);
      clearInterval(interval);
    };
  }, []);

  const handleCopyLink = () => {
    const link = qrCode?.url || `${typeof window !== "undefined" ? window.location.origin : ""}/emergency/${user?.id || "user"}`;
    navigator.clipboard.writeText(link);
    setCopied(true);
    toast({
      title: "Link copied!",
      description: "Emergency access link copied to clipboard",
    });
    setTimeout(() => setCopied(false), 2000);
  };

  const handlePrint = () => {
    window.print();
  };

  const handleShare = async () => {
    const shareData = {
      title: 'MediKey Emergency Access',
      text: 'Access my emergency medical information',
      url: qrCode?.url || `${typeof window !== "undefined" ? window.location.origin : ""}/emergency/${user?.id || "user"}`,
    };

    if (navigator.share) {
      try {
        await navigator.share(shareData);
        toast({
          title: "Shared successfully",
          description: "Emergency access information shared",
        });
      } catch (err) {
        toast({
          title: "Share failed",
          description: "Could not share emergency access information",
          variant: "destructive",
        });
      }
    } else {
      handleCopyLink();
    }
  };

  const isLoading = qrLoading || profileLoading;

  const qrValue =
    qrCode?.url || `${typeof window !== "undefined" ? window.location.origin : ""}/emergency/${user?.id || "user"}`;

  return (
    <div className="relative min-h-screen overflow-hidden bg-gradient-to-br from-orange-50 to-red-100 dark:from-gray-900 dark:to-red-950">
      {/* Particle Background */}
      <ParticleBackground
        colorScheme="orange"
        particleCount={60}
        speed={0.3}
        interactive={true}
        density={0.8}
      />

      <div className="container mx-auto py-6 px-4 sm:px-6 lg:px-8 relative z-10">
        <div className={`flex flex-col sm:flex-row sm:items-center justify-between mb-6 transition-all duration-500 ${animateIn ? 'opacity-100 translate-y-0' : 'opacity-0 -translate-y-4'}`}>
          <div className="bg-gradient-to-r from-orange-500/10 to-red-500/10 dark:from-orange-500/20 dark:to-red-500/20 p-4 rounded-xl backdrop-blur-sm">
            <h1 className="text-2xl font-semibold text-gradient text-orange-gradient flex items-center drop-shadow-lg">
              <Shield className="h-6 w-6 mr-2 text-red-500 dark:text-red-400 animate-pulse-slow" />
              Emergency Access
            </h1>
            <p className="mt-1 text-sm text-gray-700 dark:text-gray-100 p-2 rounded-md inline-block">
              Critical health information for emergency situations
            </p>
          </div>
        </div>

        <ColorfulCard
          className={`mb-6 transition-all duration-500 ${animateIn ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}
          style={{ transitionDelay: '100ms' }}
          colorScheme="gradient-orange-red"
          glowEffect={true}
          hoverAnimation="scale"
          entranceAnimation="fade-up"
        >
          <CardContent className="p-4 bg-gradient-to-r from-orange-500/10 to-red-500/10 dark:from-orange-500/20 dark:to-red-500/20 backdrop-blur-sm">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-300 mr-3 mt-0.5 animate-pulse-glow" />
              <div>
                <h3 className="text-sm font-medium text-red-800 dark:text-red-200">Important Information</h3>
                <p className="mt-1 text-sm text-red-700 dark:text-red-300">
                  Share the QR code only with emergency healthcare providers.
                </p>
              </div>
            </div>
          </CardContent>
        </ColorfulCard>

        {/* QR Code Card */}
        <ColorfulCard
          className={`mb-6 transition-all duration-500 ${animateIn ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}
          style={{ transitionDelay: '200ms' }}
          colorScheme="gradient-orange-red"
          glowEffect={true}
          floatEffect={true}
          hoverAnimation="lift"
          entranceAnimation="fade-up"
        >
          <CardHeader className="bg-gradient-to-r from-orange-500/20 to-red-500/20 dark:from-orange-600/30 dark:to-red-600/30 backdrop-blur-sm border-b border-red-200 dark:border-red-800">
            <CardTitle className="text-gradient text-orange-gradient flex items-center drop-shadow-md">
              <Shield className="h-5 w-5 mr-2 text-red-600 dark:text-red-300" />
              Emergency QR Code
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 bg-gradient-to-br from-orange-100/80 to-red-50/60 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm">
            {isLoading ? (
              <div className="flex flex-col items-center space-y-4">
                <Skeleton className="h-48 w-48" />
                <Skeleton className="h-4 w-64" />
                <Skeleton className="h-4 w-48" />
              </div>
            ) : (
              <div className="flex flex-col items-center">
                <div className="bg-gradient-to-br from-white to-orange-50 dark:from-gray-700 dark:to-gray-800 p-4 border border-orange-200 dark:border-orange-900/50 rounded-lg shadow-inner animate-pulse-glow">
                  {qrValue ? (
                    <div className="relative group">
                      <QRCodeSVG value={qrValue} size={200} />
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-all duration-300 flex items-center justify-center opacity-0 group-hover:opacity-100">
                        <ColorfulButton
                          variant="outline"
                          size="sm"
                          className="bg-white/80 hover:bg-white dark:bg-gray-800/80 dark:hover:bg-gray-800 dark:text-white"
                          onClick={() => setShowQrCode(true)}
                          colorScheme="gradient-orange-red"
                          glowEffect={true}
                        >
                          <QrCode className="h-4 w-4" />
                        </ColorfulButton>
                      </div>
                    </div>
                  ) : (
                    <p className="dark:text-white">Generating QR Code...</p>
                  )}
                </div>

                <p className="text-sm mt-4 text-gray-700 dark:text-gray-100 text-center font-medium">
                  Scan this QR code to access medical data in emergencies.
                </p>

                <div className="flex items-center justify-center bg-gradient-orange-red text-white p-3 rounded-md mt-2 animate-gradient-shift shadow-md shadow-orange-500/20">
                  <Clock className="h-5 w-5 mr-2" />
                  <p className="text-xs font-medium">
                    QR code expires in {Math.floor(countdown)} hours and {Math.floor((countdown % 1) * 60)} minutes.
                  </p>
                </div>

                <div className="flex flex-wrap gap-2 mt-4 justify-center">
                  <InteractiveTooltip content="View full screen QR code" side="top">
                    <ColorfulButton
                      colorScheme="gradient-orange-red"
                      onClick={() => setShowQrCode(true)}
                      className="flex items-center shadow-md shadow-orange-500/20 dark:text-white"
                      glowEffect={true}
                      hoverAnimation="scale"
                    >
                      <QrCode className="mr-2 h-4 w-4" />
                      View Full Screen
                    </ColorfulButton>
                  </InteractiveTooltip>

                  <InteractiveTooltip content="Copy emergency access link" side="top">
                    <ColorfulButton
                      colorScheme="gradient-orange-red"
                      onClick={handleCopyLink}
                      className="flex items-center shadow-md shadow-orange-500/20 dark:text-white"
                      glowEffect={true}
                      hoverAnimation="scale"
                    >
                      <Copy className="mr-2 h-4 w-4" />
                      {copied ? "Copied!" : "Copy Link"}
                    </ColorfulButton>
                  </InteractiveTooltip>

                  <InteractiveTooltip content="Print emergency information" side="top">
                    <ColorfulButton
                      colorScheme="gradient-orange-red"
                      onClick={handlePrint}
                      className="flex items-center shadow-md shadow-orange-500/20 dark:text-white"
                      glowEffect={true}
                      hoverAnimation="scale"
                    >
                      <Printer className="mr-2 h-4 w-4" />
                      Print
                    </ColorfulButton>
                  </InteractiveTooltip>

                  <InteractiveTooltip content="Share emergency access" side="top">
                    <ColorfulButton
                      colorScheme="gradient-orange-red"
                      onClick={handleShare}
                      className="flex items-center shadow-md shadow-orange-500/20 dark:text-white"
                      glowEffect={true}
                      hoverAnimation="scale"
                    >
                      <Share2 className="mr-2 h-4 w-4" />
                      Share
                    </ColorfulButton>
                  </InteractiveTooltip>
                </div>
              </div>
            )}
          </CardContent>
        </ColorfulCard>

        {/* Personal + Medical Info */}
        <ColorfulCard
          className={`mb-6 transition-all duration-500 ${animateIn ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`}
          style={{ transitionDelay: '300ms' }}
          colorScheme="gradient-orange-red"
          glowEffect={true}
          hoverAnimation="lift"
          entranceAnimation="fade-up"
        >
          <CardHeader className="bg-gradient-to-r from-orange-500/20 to-red-500/20 dark:from-orange-600/30 dark:to-red-600/30 backdrop-blur-sm border-b border-red-200 dark:border-red-800">
            <CardTitle className="text-gradient text-orange-gradient flex items-center drop-shadow-md">
              <Activity className="h-5 w-5 mr-2 text-red-600 dark:text-red-300 animate-pulse-slow" />
              Critical Medical Info
            </CardTitle>
          </CardHeader>
          <CardContent className="p-6 bg-gradient-to-br from-orange-100/80 to-red-50/60 dark:from-gray-800/90 dark:to-gray-900/80 backdrop-blur-sm">
            {isLoading ? (
              <div className="space-y-4">
                <Skeleton className="h-8 w-full" />
                <Skeleton className="h-24 w-full" />
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Personal Info */}
                <div className="transition-all duration-300 hover-lift p-4 rounded-lg bg-gradient-to-br from-orange-200/30 to-red-100/20 dark:from-orange-900/20 dark:to-red-900/10 backdrop-blur-sm animate-fadeIn stagger-1 shadow-md shadow-orange-500/10">
                  <h3 className="text-sm text-gradient text-orange-gradient uppercase mb-2 font-bold drop-shadow-sm">Personal Information</h3>
                  <div className="bg-gradient-to-br from-white/90 to-orange-50/80 dark:from-gray-700/90 dark:to-gray-800/80 p-4 rounded-md space-y-2 border border-orange-200 dark:border-orange-800/50 shadow-orange">
                    <p className="text-gray-800 dark:text-gray-100"><strong className="text-orange-700 dark:text-orange-300">Full Name:</strong> {userProfile?.fullName || "Not available"}</p>
                    <p className="text-gray-800 dark:text-gray-100"><strong className="text-orange-700 dark:text-orange-300">Date of Birth:</strong> {userProfile?.dateOfBirth || "Not specified"}</p>
                    <p className="text-gray-800 dark:text-gray-100"><strong className="text-orange-700 dark:text-orange-300">Gender:</strong> {userProfile?.gender || "Not specified"}</p>
                    <p className="text-gray-800 dark:text-gray-100"><strong className="text-orange-700 dark:text-orange-300">Blood Type:</strong> <span className="text-gradient text-orange-gradient font-semibold">{userProfile?.bloodType || "Not specified"}</span></p>
                  </div>
                </div>

                {/* Medical Info */}
                <div className="transition-all duration-300 hover-lift p-4 rounded-lg bg-gradient-to-br from-orange-200/30 to-red-100/20 dark:from-orange-900/20 dark:to-red-900/10 backdrop-blur-sm animate-fadeIn stagger-2 shadow-md shadow-orange-500/10">
                  <h3 className="text-sm text-gradient text-orange-gradient uppercase mb-2 font-bold drop-shadow-sm">Medical Information</h3>
                  <div className="bg-gradient-to-br from-white/90 to-orange-50/80 dark:from-gray-700/90 dark:to-gray-800/80 p-4 rounded-md space-y-2 border border-orange-200 dark:border-orange-800/50 shadow-orange">
                    <p className="text-gray-800 dark:text-gray-100"><strong className="text-orange-700 dark:text-orange-300">Allergies:</strong> {userProfile?.allergies || "None"}</p>
                    <p className="text-gray-800 dark:text-gray-100"><strong className="text-orange-700 dark:text-orange-300">Chronic Conditions:</strong> {userProfile?.chronicConditions || "None"}</p>
                    <p className="text-gray-800 dark:text-gray-100"><strong className="text-orange-700 dark:text-orange-300">Emergency Contact:</strong><br />
                      {userProfile?.emergencyContactName
                        ? <span className="animate-pulse-slow inline-block bg-orange-100/50 dark:bg-orange-900/30 p-1 rounded mt-1 text-orange-800 dark:text-orange-200">{userProfile.emergencyContactName} ({userProfile.emergencyContactPhone})</span>
                        : "Not provided"}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </ColorfulCard>

        {/* QR Code Dialog */}
        <Dialog open={showQrCode} onOpenChange={setShowQrCode}>
          <DialogContent className="sm:max-w-md bg-gradient-to-br from-orange-100/90 to-red-50/80 dark:from-gray-800/95 dark:to-gray-900/90 backdrop-blur-sm border-2 border-orange-300 dark:border-orange-800 shadow-lg shadow-orange-500/30">
            <DialogHeader className="bg-gradient-to-r from-orange-500/20 to-red-500/20 dark:from-orange-600/30 dark:to-red-600/30 p-3 rounded-t-lg">
              <DialogTitle className="text-gradient text-orange-gradient flex items-center justify-center drop-shadow-md">
                <Sparkles className="h-5 w-5 mr-2 animate-pulse-glow text-yellow-500" />
                Emergency Access QR Code
              </DialogTitle>
            </DialogHeader>
            <div className="flex flex-col items-center p-4">
              <div className="bg-gradient-to-br from-white to-orange-50 dark:from-gray-700 dark:to-gray-800 p-6 rounded-lg border-2 border-orange-200 dark:border-orange-800/50 animate-float shadow-lg shadow-orange-500/20">
                {qrValue ? <QRCodeSVG value={qrValue} size={300} /> : <p className="dark:text-white">Generating QR Code...</p>}
              </div>
              <p className="text-sm mt-4 text-gray-700 dark:text-gray-100 text-center font-medium bg-orange-100/50 dark:bg-orange-900/30 p-2 rounded-md">
                Only share with emergency healthcare providers.
              </p>
              <div className="flex gap-2 mt-4">
                <ColorfulButton
                  colorScheme="gradient-orange-red"
                  onClick={handleCopyLink}
                  className="flex items-center shadow-md shadow-orange-500/20 dark:text-white"
                  glowEffect={true}
                  hoverAnimation="scale"
                >
                  <Copy className="mr-2 h-4 w-4" />
                  {copied ? "Copied!" : "Copy Link"}
                </ColorfulButton>
                <ColorfulButton
                  colorScheme="gradient-orange-red"
                  onClick={handlePrint}
                  className="flex items-center shadow-md shadow-orange-500/20 dark:text-white"
                  glowEffect={true}
                  hoverAnimation="scale"
                >
                  <Printer className="mr-2 h-4 w-4" />
                  Print
                </ColorfulButton>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}

import { useState, useRef } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Upload, X, Camera, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { updateProfileAvatar } from "@/lib/api";

interface AvatarUploadProps {
  currentAvatarUrl?: string;
  username: string;
  fullName: string;
}

export function AvatarUpload({ currentAvatarUrl, username, fullName }: AvatarUploadProps) {
  const [avatarUrl, setAvatarUrl] = useState<string | undefined>(currentAvatarUrl);
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | undefined>(currentAvatarUrl);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const updateAvatarMutation = useMutation({
    mutationFn: async (data: { avatarUrl: string }) => {
      return updateProfileAvatar(data.avatarUrl);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/users/profile"] });
      toast({
        title: "Profile picture updated",
        description: "Your profile picture has been updated successfully.",
      });
      setIsUploading(false);
    },
    onError: (error) => {
      toast({
        title: "Update failed",
        description: `Failed to update profile picture: ${error.message}`,
        variant: "destructive",
      });
      setIsUploading(false);
      // Revert to previous avatar
      setPreviewUrl(avatarUrl);
    },
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];

      // Check file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please select an image under 5MB.",
          variant: "destructive",
        });
        return;
      }

      // Check file type
      if (!file.type.startsWith("image/")) {
        toast({
          title: "Invalid file type",
          description: "Please select an image file.",
          variant: "destructive",
        });
        return;
      }

      setIsUploading(true);

      // Create a preview
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          setPreviewUrl(event.target.result as string);

          // In a real app, you would upload the file to a server here
          // For this demo, we'll simulate an upload by using the data URL
          setTimeout(() => {
            const dataUrl = event.target?.result as string;
            updateAvatarMutation.mutate({ avatarUrl: dataUrl });
            setAvatarUrl(dataUrl);
          }, 1000);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRemoveAvatar = () => {
    setIsUploading(true);
    updateAvatarMutation.mutate({ avatarUrl: "" });
    setPreviewUrl(undefined);
    setAvatarUrl(undefined);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const getInitials = () => {
    if (fullName) {
      return fullName.split(" ").map(name => name[0]).join("").toUpperCase();
    }
    return username.charAt(0).toUpperCase();
  };

  return (
    <div className="flex flex-col items-center space-y-4">
      <div className="relative group">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-blue-500/20 dark:from-purple-400/20 dark:to-blue-400/20 rounded-full blur-md animate-pulse-slow"></div>
        <Avatar className="h-32 w-32 border-4 border-white dark:border-gray-800 shadow-lg relative z-10">
          <AvatarImage src={previewUrl || ""} alt={fullName || username} />
          <AvatarFallback className="bg-gradient-to-br from-purple-500 to-blue-500 dark:from-purple-400 dark:to-blue-400 text-white text-2xl">
            {getInitials()}
          </AvatarFallback>
        </Avatar>

        {isUploading && (
          <div className="absolute inset-0 flex items-center justify-center bg-black/30 rounded-full z-20">
            <Loader2 className="h-8 w-8 text-white animate-spin" />
          </div>
        )}
      </div>

      <div className="flex space-x-2">
        <input
          ref={fileInputRef}
          type="file"
          id="avatar-upload"
          className="hidden"
          accept="image/*"
          onChange={handleFileChange}
          disabled={isUploading}
        />

        <Button
          type="button"
          variant="outline"
          size="sm"
          className="flex items-center"
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
        >
          <Camera className="h-4 w-4 mr-2" />
          {previewUrl ? "Change Picture" : "Upload Picture"}
        </Button>

        {previewUrl && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            className="flex items-center text-red-500 hover:text-red-600"
            onClick={handleRemoveAvatar}
            disabled={isUploading}
          >
            <X className="h-4 w-4 mr-2" />
            Remove
          </Button>
        )}
      </div>

      <p className="text-xs text-gray-500 dark:text-gray-400 text-center max-w-xs">
        Upload a profile picture (JPG, PNG, GIF). Maximum file size: 5MB.
      </p>
    </div>
  );
}

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format, isToday, isTomorrow, addDays, isBefore, isAfter } from "date-fns";
import {
  CalendarIcon, Clock, Plus, MapPin, Calendar as CalendarIcon2, User,
  Stethoscope
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { ColorfulButton } from "@/components/ui/colorful-button";
import { ColorfulCard } from "@/components/ui/colorful-card";
import { ParticleBackground } from "@/components/ui/particle-background";
import { InteractiveTooltip } from "@/components/ui/interactive-tooltip";

// Define interfaces instead of relying on imported schema
interface Appointment {
  id: string;
  title: string;
  description?: string;
  appointmentType: string;
  providerName: string;
  providerType?: string;
  location?: string;
  appointmentDate: string;
  duration?: number;
  reminderSet: boolean;
  reminderTime?: string;
  notes?: string;
}

interface AppointmentFormData {
  title: string;
  description?: string;
  appointmentType: string;
  providerName: string;
  providerType?: string;
  location?: string;
  appointmentDate: Date;
  appointmentTime: string;
  duration?: number;
  reminderSet: boolean;
  notes?: string;
}

// Define the AppointmentCard component props
interface AppointmentCardProps {
  appointment: Appointment;
  isPast?: boolean;
}

// Define the AppointmentCard component
const AppointmentCard = ({ appointment, isPast = false }: AppointmentCardProps) => {
  const appointmentDate = new Date(appointment.appointmentDate);
  const isUpcoming = isAfter(appointmentDate, new Date());
  const displayDate = isToday(appointmentDate)
    ? "Today"
    : isTomorrow(appointmentDate)
    ? "Tomorrow"
    : format(appointmentDate, "MMM d, yyyy");

  return (
    <div className="relative overflow-hidden rounded-lg mb-4">
      <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-blue-500/5 dark:from-purple-500/10 dark:to-blue-500/10 rounded-lg"></div>
      <div className="p-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-purple-100 dark:border-purple-900/30 rounded-lg">
        <div className="flex justify-between items-start mb-2">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 dark:text-gray-100">{appointment.title}</h3>
            <div className="flex items-center text-sm text-gray-600 dark:text-gray-400 mt-1">
              <CalendarIcon2 className="h-3.5 w-3.5 mr-1 text-purple-500 dark:text-purple-400" />
              <span>{displayDate}</span>
              <span className="mx-1">•</span>
              <Clock className="h-3.5 w-3.5 mr-1 text-purple-500 dark:text-purple-400" />
              <span>{format(appointmentDate, "h:mm a")}</span>
            </div>
          </div>
          <div className="flex items-center">
            <div className={`px-2 py-1 text-xs rounded-full ${
              isPast 
                ? "bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400" 
                : "bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300"
            }`}>
              {isPast ? "Past" : "Upcoming"}
            </div>
          </div>
        </div>
        
        {appointment.description && (
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 mb-3">{appointment.description}</p>
        )}
        
        <div className="grid grid-cols-2 gap-3 mt-3">
          <div className="flex items-start">
            <Stethoscope className="h-4 w-4 mr-2 text-purple-500 dark:text-purple-400 mt-0.5" />
            <div>
              <div className="text-xs text-gray-500 dark:text-gray-500">Provider</div>
              <div className="text-sm text-gray-800 dark:text-gray-200">{appointment.providerName}</div>
              {appointment.providerType && (
                <div className="text-xs text-gray-500 dark:text-gray-400">{appointment.providerType}</div>
              )}
            </div>
          </div>
          
          <div className="flex items-start">
            <MapPin className="h-4 w-4 mr-2 text-purple-500 dark:text-purple-400 mt-0.5" />
            <div>
              <div className="text-xs text-gray-500 dark:text-gray-500">Location</div>
              <div className="text-sm text-gray-800 dark:text-gray-200">{appointment.location || "Not specified"}</div>
            </div>
          </div>
        </div>
        
        <div className="flex items-center justify-between mt-4">
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {appointment.duration ? `${appointment.duration} minutes` : "Duration not specified"}
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm" className="h-8 text-xs">
              Reschedule
            </Button>
            <Button variant="outline" size="sm" className="h-8 text-xs">
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function Appointments() {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [formData, setFormData] = useState<AppointmentFormData>({
    title: "",
    description: "",
    appointmentType: "checkup",
    providerName: "",
    providerType: "",
    location: "",
    appointmentTime: "09:00",
    duration: 30,
    reminderSet: true,
    notes: "",
    appointmentDate: new Date(),
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [animateItems, setAnimateItems] = useState(false);
  const [activeTab, setActiveTab] = useState("upcoming");
  const [hoveredAppointmentId, setHoveredAppointmentId] = useState<string | null>(null);

  useEffect(() => {
    // Animate items after a short delay for a staggered effect
    const timer = setTimeout(() => {
      setAnimateItems(true);
    }, 300);

    return () => {
      clearTimeout(timer);
    };
  }, []);

  const { data: appointments, isLoading } = useQuery<Appointment[]>({
    queryKey: ["/api/appointments"],
  });

  const addAppointmentMutation = useMutation({
    mutationFn: (data: AppointmentFormData) => {
      try {
        // Format the date and time properly
        const formattedDate = new Date(data.appointmentDate);

        // Handle time input
        if (data.appointmentTime) {
          const [hours, minutes] = data.appointmentTime.split(':').map(Number);
          formattedDate.setHours(hours || 0, minutes || 0, 0, 0);
        }

        const appointmentData = {
          ...data,
          appointmentDate: formattedDate.toISOString(),
          reminderTime: data.reminderSet ? addDays(formattedDate, -1).toISOString() : null,
        };

        // Remove the appointmentTime field as it's not needed in the final data
        const { appointmentTime, ...finalData } = appointmentData;

        return apiRequest("POST", "/api/appointments", finalData);
      } catch (error) {
        console.error("Error formatting appointment data:", error);
        throw new Error("Failed to format appointment data. Please check all fields.");
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/appointments"] });
      setIsAddModalOpen(false);
      setFormData({
        title: "",
        description: "",
        appointmentType: "checkup",
        providerName: "",
        providerType: "",
        location: "",
        appointmentTime: "09:00",
        duration: 30,
        reminderSet: true,
        notes: "",
        appointmentDate: new Date(),
      });
      toast({
        title: "Success",
        description: "Appointment scheduled successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: `Failed to schedule appointment: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  const validateForm = (data: AppointmentFormData) => {
    const newErrors: Record<string, string> = {};

    if (!data.title) newErrors.title = "Title is required";
    if (!data.appointmentType) newErrors.appointmentType = "Appointment type is required";
    if (!data.providerName) newErrors.providerName = "Provider name is required";
    if (!data.appointmentDate) newErrors.appointmentDate = "Appointment date is required";
    if (!data.appointmentTime) newErrors.appointmentTime = "Appointment time is required";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof AppointmentFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error for this field if it exists
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const onSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (validateForm(formData)) {
        addAppointmentMutation.mutate(formData);
      }
    } catch (error) {
      console.error('Error submitting appointment:', error);
      toast({
        title: 'Error',
        description: 'Failed to schedule appointment. Please try again.',
        variant: 'destructive'
      });
    }
  };

  // Filter appointments
  const pastAppointments = appointments
    ? appointments
        .filter(app => isBefore(new Date(app.appointmentDate), new Date()))
        .sort((a, b) => new Date(b.appointmentDate).getTime() - new Date(a.appointmentDate).getTime())
    : [];

  const upcomingAppointments = appointments
    ? appointments
        .filter(app => isAfter(new Date(app.appointmentDate), new Date()))
        .sort((a, b) => new Date(a.appointmentDate).getTime() - new Date(b.appointmentDate).getTime())
    : [];

  const todaysAppointments = upcomingAppointments.filter(app =>
    isToday(new Date(app.appointmentDate))
  );

  return (
    <div className="min-h-screen relative bg-gradient-to-br from-purple-50 to-indigo-100 dark:from-gray-800 dark:via-purple-950 dark:to-blue-950 py-6 px-4 sm:px-6 lg:px-8 transition-all duration-500">
      {/* Particle Background */}
      <ParticleBackground
        colorScheme="purple"
        particleCount={80}
        speed={0.3}
        interactive={true}
        density={0.8}
      />

      {/* Animated background glow effects */}
      <div className="fixed inset-0 -z-5 overflow-hidden opacity-70 dark:opacity-40">
        <div className="absolute top-1/4 -left-20 w-72 h-72 bg-purple-500/20 dark:bg-purple-500/10 rounded-full filter blur-3xl animate-pulse-slow"></div>
        <div className="absolute bottom-1/4 -right-20 w-72 h-72 bg-blue-500/20 dark:bg-blue-500/10 rounded-full filter blur-3xl animate-pulse-slow" style={{ animationDelay: '1s' }}></div>
      </div>

      {/* Page Header */}
      <div className="mb-8 relative group">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-indigo-600/5 to-blue-600/5 dark:from-purple-400/10 dark:via-indigo-400/10 dark:to-blue-400/10 rounded-xl transform transition-all duration-300 group-hover:scale-105 group-hover:opacity-80"></div>
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/5 via-indigo-600/5 to-blue-600/5 dark:from-purple-400/10 dark:via-indigo-400/10 dark:to-blue-400/10 rounded-xl blur-xl opacity-50 transform transition-all duration-300 group-hover:opacity-100"></div>

        <div className="relative bg-gradient-to-r from-purple-500/10 via-indigo-500/10 to-blue-500/10 dark:from-purple-500/20 dark:via-indigo-500/20 dark:to-blue-500/20 p-6 rounded-xl backdrop-blur-sm animate-fadeIn border border-white/10 dark:border-white/5 shadow-lg">
          <div className="flex flex-col sm:flex-row sm:items-center justify-between">
            <div className="flex items-start">
              <div className="relative mr-4">
                <div className="absolute inset-0 bg-purple-500/30 dark:bg-purple-400/20 rounded-full filter blur-md animate-pulse-slow"></div>
                <CalendarIcon2 className="h-10 w-10 text-purple-600 dark:text-purple-300 relative z-10 animate-float" />
              </div>

              <div className="flex-1">
                <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-300 dark:to-blue-300 animate-gradient-shift">
                  Appointments
                </h1>
                <div className="h-1 w-0 bg-gradient-to-r from-purple-500 to-blue-600 dark:from-purple-400 dark:to-blue-400 mt-1 rounded-full animate-expand"></div>

                <p className="mt-3 text-base text-gray-700 dark:text-gray-200">
                  Manage your medical appointments and follow-ups
                </p>
              </div>
            </div>

            <div className="mt-4 sm:mt-0">
              <ColorfulButton
                colorScheme="purple"
                glowEffect={true}
                hoverAnimation="scale"
                className="relative overflow-hidden group"
                onClick={() => setIsAddModalOpen(true)}
              >
                <span className="absolute inset-0 w-full h-full bg-gradient-to-r from-purple-600/20 to-blue-600/20 dark:from-purple-400/20 dark:to-blue-400/20 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left"></span>
                <span className="relative flex items-center">
                  <Plus className="mr-2 h-4 w-4" />
                  Schedule Appointment
                </span>
              </ColorfulButton>
            </div>
          </div>

          <div className="mt-4 flex flex-wrap gap-3">
            <div className="flex items-center space-x-2 bg-purple-500/10 dark:bg-purple-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.3s' }}>
              <div className="w-2 h-2 rounded-full bg-purple-500 dark:bg-purple-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-purple-700 dark:text-purple-300">Appointment Reminders</p>
            </div>

            <div className="flex items-center space-x-2 bg-blue-500/10 dark:bg-blue-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.5s' }}>
              <div className="w-2 h-2 rounded-full bg-blue-500 dark:bg-blue-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-blue-700 dark:text-blue-300">Calendar Integration</p>
            </div>

            <div className="flex items-center space-x-2 bg-indigo-500/10 dark:bg-indigo-500/20 px-3 py-1.5 rounded-full animate-fadeIn" style={{ animationDelay: '0.7s' }}>
              <div className="w-2 h-2 rounded-full bg-indigo-500 dark:bg-indigo-400 animate-pulse-slow"></div>
              <p className="text-xs font-medium text-indigo-700 dark:text-indigo-300">Follow-up Tracking</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <ColorfulCard
            className="overflow-hidden animate-fadeIn"
            colorScheme="purple"
            glowEffect={true}
            entranceAnimation="fade-up"
          >
            <div className="p-4 bg-gradient-to-br from-purple-100/70 to-blue-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm">
              <Tabs
                defaultValue="upcoming"
                className="w-full"
                onValueChange={(value) => setActiveTab(value)}
              >
                <div className="relative">
                  <div className="absolute inset-x-0 bottom-0 h-px bg-gradient-to-r from-purple-200 via-blue-200 to-purple-200 dark:from-purple-800 dark:via-blue-800 dark:to-purple-800"></div>
                  <TabsList className="mb-4 bg-transparent relative z-10 space-x-2">
                    <TabsTrigger
                      value="upcoming"
                      className={`
                        relative overflow-hidden transition-all duration-300
                        data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/10 data-[state=active]:to-blue-500/10
                        data-[state=active]:dark:from-purple-500/20 data-[state=active]:dark:to-blue-500/20
                        data-[state=active]:text-purple-700 data-[state=active]:dark:text-purple-300
                        data-[state=active]:border-b-2 data-[state=active]:border-purple-500 data-[state=active]:dark:border-purple-400
                        data-[state=active]:shadow-sm
                      `}
                    >
                      Upcoming
                    </TabsTrigger>
                    <TabsTrigger
                      value="today"
                      className={`
                        relative overflow-hidden transition-all duration-300
                        data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/10 data-[state=active]:to-blue-500/10
                        data-[state=active]:dark:from-purple-500/20 data-[state=active]:dark:to-blue-500/20
                        data-[state=active]:text-purple-700 data-[state=active]:dark:text-purple-300
                        data-[state=active]:border-b-2 data-[state=active]:border-purple-500 data-[state=active]:dark:border-purple-400
                        data-[state=active]:shadow-sm
                      `}
                    >
                      Today
                    </TabsTrigger>
                    <TabsTrigger
                      value="past"
                      className={`
                        relative overflow-hidden transition-all duration-300
                        data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500/10 data-[state=active]:to-blue-500/10
                        data-[state=active]:dark:from-purple-500/20 data-[state=active]:dark:to-blue-500/20
                        data-[state=active]:text-purple-700 data-[state=active]:dark:text-purple-300
                        data-[state=active]:border-b-2 data-[state=active]:border-purple-500 data-[state=active]:dark:border-purple-400
                        data-[state=active]:shadow-sm
                      `}
                    >
                      Past
                    </TabsTrigger>
                  </TabsList>
                </div>

                <TabsContent value="upcoming" className="space-y-4">
                  {isLoading ? (
                    <div className="flex justify-center items-center h-40">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                    </div>
                  ) : upcomingAppointments.length > 0 ? (
                    <div className="space-y-4">
                      {upcomingAppointments.map((appointment, index) => (
                        <div
                          key={appointment.id}
                          className={`transition-all duration-500 transform ${
                            animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
                          }`}
                          style={{ transitionDelay: `${index * 0.1}s` }}
                          onMouseEnter={() => setHoveredAppointmentId(appointment.id)}
                          onMouseLeave={() => setHoveredAppointmentId(null)}
                        >
                          <AppointmentCard appointment={appointment} />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-100 dark:bg-purple-900/30 mb-4">
                        <CalendarIcon2 className="h-8 w-8 text-purple-500 dark:text-purple-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No upcoming appointments</h3>
                      <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
                        You don't have any upcoming appointments scheduled. Would you like to schedule one now?
                      </p>
                      <ColorfulButton
                        colorScheme="purple"
                        onClick={() => setIsAddModalOpen(true)}
                        hoverAnimation="scale"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Schedule Appointment
                      </ColorfulButton>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="today" className="space-y-4">
                  {isLoading ? (
                    <div className="flex justify-center items-center h-40">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                    </div>
                  ) : todaysAppointments.length > 0 ? (
                    <div className="space-y-4">
                      {todaysAppointments.map((appointment, index) => (
                        <div
                          key={appointment.id}
                          className={`transition-all duration-500 transform ${
                            animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
                          }`}
                          style={{ transitionDelay: `${index * 0.1}s` }}
                        >
                          <AppointmentCard appointment={appointment} />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-100 dark:bg-purple-900/30 mb-4">
                        <CalendarIcon2 className="h-8 w-8 text-purple-500 dark:text-purple-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No appointments today</h3>
                      <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
                        You don't have any appointments scheduled for today.
                      </p>
                      <ColorfulButton
                        colorScheme="purple"
                        onClick={() => setIsAddModalOpen(true)}
                        hoverAnimation="scale"
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Schedule Appointment
                      </ColorfulButton>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="past" className="space-y-4">
                  {isLoading ? (
                    <div className="flex justify-center items-center h-40">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
                    </div>
                  ) : pastAppointments.length > 0 ? (
                    <div className="space-y-4">
                      {pastAppointments.map((appointment, index) => (
                        <div
                          key={appointment.id}
                          className={`transition-all duration-500 transform ${
                            animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'
                          }`}
                          style={{ transitionDelay: `${index * 0.1}s` }}
                        >
                          <AppointmentCard appointment={appointment} isPast={true} />
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-10">
                      <div className="inline-flex items-center justify-center w-16 h-16 rounded-full bg-purple-100 dark:bg-purple-900/30 mb-4">
                        <CalendarIcon2 className="h-8 w-8 text-purple-500 dark:text-purple-400" />
                      </div>
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No past appointments</h3>
                      <p className="text-gray-500 dark:text-gray-400 mb-6 max-w-md mx-auto">
                        You don't have any past appointments in your history.
                      </p>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </div>
          </ColorfulCard>
        </div>

        <div className={`transition-all duration-500 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4'}`} style={{ transitionDelay: '300ms' }}>
          <ColorfulCard
            colorScheme="purple"
            glowEffect={true}
            hoverAnimation="lift"
            entranceAnimation="fade-up"
            className="overflow-hidden"
          >
            <div className="p-4 bg-gradient-to-br from-purple-100/70 to-blue-50/50 dark:from-gray-800 dark:to-gray-900 backdrop-blur-sm">
              <div className="flex items-center mb-4">
                <div className="relative mr-3">
                  <div className="absolute inset-0 bg-purple-400/20 dark:bg-purple-400/30 rounded-full blur-sm animate-pulse-slow"></div>
                  <CalendarIcon2 className="h-6 w-6 text-purple-700 dark:text-purple-300 relative z-10" />
                </div>
                <h3 className="text-lg font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-300 dark:to-blue-300">
                  Calendar
                </h3>
              </div>

              <div className="bg-white/80 dark:bg-gray-800/80 rounded-lg p-4 backdrop-blur-sm">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  className="rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800"
                  classNames={{
                    day_selected: "bg-purple-600 dark:bg-purple-500 text-white hover:bg-purple-700 dark:hover:bg-purple-600",
                    day_today: "bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300",
                    day_outside: "text-gray-400 dark:text-gray-600 opacity-50"
                  }}
                  components={{
                    DayContent: (props) => {
                      const date = props.date;
                      const hasAppointment = Array.isArray(appointments) && appointments.some(app => {
                        if (!app || !app.appointmentDate) return false;
                        const appDate = new Date(app.appointmentDate);
                        return (
                          date.getDate() === appDate.getDate() &&
                          date.getMonth() === appDate.getMonth() &&
                          date.getFullYear() === appDate.getFullYear()
                        );
                      });

                      return (
                        <div className="relative h-full w-full p-2">
                          <span>{props.date.getDate()}</span>
                          {hasAppointment && (
                            <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-purple-500 dark:bg-purple-400 rounded-full animate-pulse-slow"></div>
                          )}
                        </div>
                      );
                    },
                  }}
                />

              <div className="mt-6">
                <h4 className="text-base font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-300 dark:to-blue-300 mb-3 flex items-center">
                  <div className="relative mr-2">
                    <div className="absolute inset-0 bg-purple-400/20 dark:bg-purple-400/30 rounded-full blur-sm animate-pulse-slow"></div>
                    <Calendar className="h-4 w-4 text-purple-700 dark:text-purple-300 relative z-10" />
                  </div>
                  {date ? format(date, "MMMM d, yyyy") : "Select a date"}
                </h4>

                {date && appointments ? (
                  <div className="space-y-2">
                    {appointments
                      .filter(app => {
                        const appDate = new Date(app.appointmentDate);
                        return (
                          date.getDate() === appDate.getDate() &&
                          date.getMonth() === appDate.getMonth() &&
                          date.getFullYear() === appDate.getFullYear()
                        );
                      })
                      .map((app, index) => (
                        <div
                          key={app.id}
                          className={`relative overflow-hidden rounded-lg transition-all duration-300 transform ${animateItems ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-2'}`}
                          style={{ animationDelay: `${index * 0.1}s` }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-blue-500/5 dark:from-purple-500/10 dark:to-blue-500/10 rounded-lg"></div>
                          <div className="p-3 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-purple-100 dark:border-purple-900/30 rounded-lg">
                            <div className="font-medium text-gray-800 dark:text-gray-200">{app.title}</div>
                            <div className="text-gray-600 dark:text-gray-400 flex items-center text-sm mt-1">
                              <Clock className="h-3 w-3 mr-1 text-purple-500 dark:text-purple-400" />
                              {format(new Date(app.appointmentDate), "h:mm a")}

                              {app.location && (
                                <>
                                  <span className="mx-2">•</span>
                                  <MapPin className="h-3 w-3 mr-1 text-purple-500 dark:text-purple-400" />
                                  <span>{app.location}</span>
                                </>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}

                    {!appointments.some(app => {
                      const appDate = new Date(app.appointmentDate);
                      return (
                        date.getDate() === appDate.getDate() &&
                        date.getMonth() === appDate.getMonth() &&
                        date.getFullYear() === appDate.getFullYear()
                      );
                    }) && (
                      <div className="p-4 text-center">
                        <div className="text-sm text-gray-500 dark:text-gray-400 flex flex-col items-center">
                          <Calendar className="h-8 w-8 text-gray-300 dark:text-gray-600 mb-2" />
                          <span>No appointments on this date</span>
                          <ColorfulButton
                            colorScheme="purple"
                            className="mt-3 text-xs"
                            hoverAnimation="scale"
                            onClick={() => setIsAddModalOpen(true)}
                          >
                            <Plus className="h-3 w-3 mr-1" />
                            Add Appointment
                          </ColorfulButton>
                        </div>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="p-4 text-center">
                    <div className="text-sm text-gray-500 dark:text-gray-400 animate-pulse">
                      Loading appointments...
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </ColorfulCard>
        </div>
      </div>

      {/* Add Appointment Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent className="sm:max-w-[525px] bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm border-purple-200 dark:border-purple-800/30">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 to-blue-500/5 dark:from-purple-500/10 dark:to-blue-500/10 rounded-lg -z-10"></div>
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-600 to-blue-600 dark:from-purple-300 dark:to-blue-300">
              Schedule New Appointment
            </DialogTitle>
            <DialogDescription>
              Fill in the details below to schedule your medical appointment.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={onSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="title" className="text-right">
                  Title <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange("title", e.target.value)}
                  className="col-span-3"
                  placeholder="Annual Check-up"
                />
                {errors.title && (
                  <div className="col-span-4 col-start-2 text-red-500 text-sm">
                    {errors.title}
                  </div>
                )}
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange("description", e.target.value)}
                  className="col-span-3"
                  placeholder="Brief description of the appointment"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="appointmentType" className="text-right">
                  Type <span className="text-red-500">*</span>
                </Label>
                <Select
                  value={formData.appointmentType}
                  onValueChange={(value) => handleInputChange("appointmentType", value)}
                >
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select appointment type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="checkup">Regular Check-up</SelectItem>
                    <SelectItem value="follow_up">Follow-up</SelectItem>
                    <SelectItem value="specialist">Specialist Consultation</SelectItem>
                    <SelectItem value="test">Medical Test</SelectItem>
                    <SelectItem value="procedure">Medical Procedure</SelectItem>
                    <SelectItem value="vaccination">Vaccination</SelectItem>
                    <SelectItem value="therapy">Therapy Session</SelectItem>
                    <SelectItem value="dental">Dental Appointment</SelectItem>
                    <SelectItem value="vision">Vision Appointment</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                {errors.appointmentType && (
                  <div className="col-span-4 col-start-2 text-red-500 text-sm">
                    {errors.appointmentType}
                  </div>
                )}
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="providerName" className="text-right">
                  Provider <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="providerName"
                  value={formData.providerName}
                  onChange={(e) => handleInputChange("providerName", e.target.value)}
                  className="col-span-3"
                  placeholder="Dr. Smith"
                />
                {errors.providerName && (
                  <div className="col-span-4 col-start-2 text-red-500 text-sm">
                    {errors.providerName}
                  </div>
                )}
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="providerType" className="text-right">
                  Provider Type
                </Label>
                <Input
                  id="providerType"
                  value={formData.providerType}
                  onChange={(e) => handleInputChange("providerType", e.target.value)}
                  className="col-span-3"
                  placeholder="Cardiologist"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="location" className="text-right">
                  Location
                </Label>
                <Input
                  id="location"
                  value={formData.location}
                  onChange={(e) => handleInputChange("location", e.target.value)}
                  className="col-span-3"
                  placeholder="City Hospital, Room 302"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="appointmentDate" className="text-right">
                  Date <span className="text-red-500">*</span>
                </Label>
                <div className="col-span-3">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full justify-start text-left font-normal",
                          !formData.appointmentDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {formData.appointmentDate ? (
                          format(formData.appointmentDate, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={formData.appointmentDate}
                        onSelect={(date) => handleInputChange("appointmentDate", date)}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                {errors.appointmentDate && (
                  <div className="col-span-4 col-start-2 text-red-500 text-sm">
                    {errors.appointmentDate}
                  </div>
                )}
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="appointmentTime" className="text-right">
                  Time <span className="text-red-500">*</span>
                </Label>
                <Input
                  id="appointmentTime"
                  type="time"
                  value={formData.appointmentTime}
                  onChange={(e) => handleInputChange("appointmentTime", e.target.value)}
                  className="col-span-3"
                />
                {errors.appointmentTime && (
                  <div className="col-span-4 col-start-2 text-red-500 text-sm">
                    {errors.appointmentTime}
                  </div>
                )}
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="duration" className="text-right">
                  Duration (min)
                </Label>
                <Input
                  id="duration"
                  type="number"
                  value={formData.duration?.toString() || ""}
                  onChange={(e) => handleInputChange("duration", parseInt(e.target.value) || undefined)}
                  className="col-span-3"
                  placeholder="30"
                  min="5"
                  step="5"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="reminderSet" className="text-right">
                  Set Reminder
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <input
                    type="checkbox"
                    id="reminderSet"
                    checked={formData.reminderSet}
                    onChange={(e) => handleInputChange("reminderSet", e.target.checked)}
                    className="rounded border-gray-300 text-purple-600 focus:ring-purple-500"
                  />
                  <Label htmlFor="reminderSet" className="text-sm text-gray-600 dark:text-gray-400">
                    Remind me 1 day before the appointment
                  </Label>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="notes" className="text-right">
                  Notes
                </Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  className="col-span-3"
                  placeholder="Any additional notes or instructions"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)}>
                Cancel
              </Button>
              <ColorfulButton
                type="submit"
                colorScheme="purple"
                disabled={addAppointmentMutation.isPending}
                className="relative"
              >
                {addAppointmentMutation.isPending ? (
                  <>
                    <span className="opacity-0">Schedule</span>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="h-5 w-5 animate-spin rounded-full border-b-2 border-white"></div>
                    </div>
                  </>
                ) : (
                  "Schedule"
                )}
              </ColorfulButton>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
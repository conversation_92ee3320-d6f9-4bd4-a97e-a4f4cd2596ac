import { createContext, useContext, useEffect, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useLocation } from "wouter";
import { getCurrentUser as getApiUser, loginUser, logoutUser, registerUser } from "@/lib/api";
import { useToast } from "@/hooks/use-toast";
import { User } from "@/lib/types";
import { auth, signInWithGoogle, signOutUser } from "@/lib/firebase";
import { onAuthStateChanged } from "firebase/auth";

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<any>;
  register: (userData: any) => Promise<any>;
  logout: () => Promise<void>;
  loginWithGoogle: () => Promise<any>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  isAuthenticated: false,
  isLoading: true,
  login: async () => {},
  register: async () => {},
  logout: async () => {},
  loginWithGoogle: async () => {},
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [basePath, setBasePath] = useState('');

  // Determine the base path for GitHub Pages
  useEffect(() => {
    const isGitHubPages = window.location.hostname.includes('github.io');
    setBasePath(isGitHubPages ? '/medikey' : '');
  }, []);

  // Listen for Firebase auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (firebaseUser) => {
      if (firebaseUser) {
        // User is signed in
        const userObj: User = {
          id: firebaseUser.uid,
          username: firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'user',
          fullName: firebaseUser.displayName || '',
          email: firebaseUser.email || '',
          avatar: firebaseUser.photoURL || undefined
        };
        setUser(userObj);
        console.log("Firebase auth: User signed in", userObj);
      } else {
        // User is signed out
        // Try to get user from localStorage (for mock API)
        const storedUser = localStorage.getItem('medikey_current_user');
        if (storedUser) {
          setUser(JSON.parse(storedUser));
          console.log("Local storage: User found");
        } else {
          setUser(null);
          console.log("No user found");
        }
      }
      setIsLoading(false);
    });

    return () => unsubscribe();
  }, []);

  // Google Sign-In function
  const loginWithGoogle = async () => {
    try {
      const user = await signInWithGoogle();
      setUser(user);

      console.log('Google sign-in successful, navigating to dashboard');
      console.log('Base path:', basePath);

      // Use a more reliable navigation method
      window.location.href = `${window.location.origin}${basePath}/dashboard`;

      toast({
        title: "Welcome!",
        description: "You've successfully signed in with Google.",
      });
      return user;
    } catch (error: any) {
      console.error("Google sign-in error:", error);
      toast({
        title: "Sign in failed",
        description: error.message || "Could not sign in with Google",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Traditional login (using mock API)
  const login = async (username: string, password: string) => {
    try {
      const result = await loginUser(username, password);
      // The mock API will handle storing the user in localStorage
      // Refresh the user state
      const storedUser = localStorage.getItem('medikey_current_user');
      if (storedUser) {
        setUser(JSON.parse(storedUser));
      }

      console.log('Login successful, navigating to dashboard');
      console.log('Base path:', basePath);

      // Use a more reliable navigation method
      window.location.href = `${window.location.origin}${basePath}/dashboard`;

      toast({
        title: "Welcome back!",
        description: "You've successfully logged in.",
      });
      return result;
    } catch (error: any) {
      console.error("Login error:", error);
      toast({
        title: "Login failed",
        description: error.message || "Invalid username or password",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Registration (using mock API)
  const register = async (userData: any) => {
    try {
      const result = await registerUser(userData);

      console.log('Registration successful, navigating to login page');
      console.log('Base path:', basePath);

      // Use a more reliable navigation method
      window.location.href = `${window.location.origin}${basePath}/login`;

      toast({
        title: "Registration successful",
        description: "Your account has been created. Please log in.",
      });
      return result;
    } catch (error: any) {
      console.error("Registration error:", error);
      toast({
        title: "Registration failed",
        description: error.message || "Could not create account",
        variant: "destructive",
      });
      throw error;
    }
  };

  // Logout function
  const logout = async () => {
    try {
      // Sign out from Firebase
      await signOutUser();
      // Also clear localStorage (for mock API)
      localStorage.removeItem('medikey_current_user');
      setUser(null);

      console.log('Logout successful, navigating to login page');
      console.log('Base path:', basePath);

      // Use a more reliable navigation method
      window.location.href = `${window.location.origin}${basePath}/login`;

      toast({
        title: "Logged out",
        description: "You've been successfully logged out.",
      });
    } catch (error: any) {
      console.error("Logout error:", error);
      toast({
        title: "Logout failed",
        description: error.message || "Could not log out",
        variant: "destructive",
      });
    }
  };

  const contextValue = {
    user: user || null,
    isAuthenticated: !!user,
    isLoading,
    login,
    register,
    logout,
    loginWithGoogle
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

export const useAuth = () => useContext(AuthContext);

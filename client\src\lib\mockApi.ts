// Mock API implementation for GitHub Pages deployment
// This allows the frontend to work without a real backend

interface User {
  id: string;
  username: string;
  fullName: string;
  email: string;
  avatar?: string;
}

interface StoredUser extends User {
  password: string;
}

// Helper to get users from localStorage
const getStoredUsers = (): Record<string, StoredUser> => {
  const users = localStorage.getItem('medikey_users');
  return users ? JSON.parse(users) : {};
};

// Helper to save users to localStorage
const saveStoredUsers = (users: Record<string, StoredUser>) => {
  localStorage.setItem('medikey_users', JSON.stringify(users));
};

// Helper to get current user from localStorage
const getCurrentUserFromStorage = (): User | null => {
  const currentUser = localStorage.getItem('medikey_current_user');
  return currentUser ? JSON.parse(currentUser) : null;
};

// Helper to save current user to localStorage
const saveCurrentUserToStorage = (user: User | null) => {
  if (user) {
    localStorage.setItem('medikey_current_user', JSON.stringify(user));
  } else {
    localStorage.removeItem('medikey_current_user');
  }
};

// Mock API endpoints
export const mockApi = {
  // Auth endpoints
  auth: {
    register: async (userData: { username: string; password: string; fullName: string; email: string }): Promise<{ message: string; id: string }> => {
      console.log("Mock API - Register called with:", { ...userData, password: '***' });

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      try {
        const users = getStoredUsers();

        // Check if username already exists
        if (users[userData.username]) {
          console.log("Mock API - Registration failed: Username already exists");
          throw new Error('Username already exists');
        }

        // Create new user
        const newUser: StoredUser = {
          id: Date.now().toString(),
          username: userData.username,
          password: userData.password, // In a real app, this would be hashed
          fullName: userData.fullName,
          email: userData.email
        };

        // Save user
        users[userData.username] = newUser;
        saveStoredUsers(users);

        console.log("Mock API - Registration successful:", { id: newUser.id, username: newUser.username });
        return { message: 'User created successfully', id: newUser.id };
      } catch (error) {
        console.error("Mock API - Registration error:", error);
        throw error;
      }
    },

    login: async (credentials: { username: string; password: string }): Promise<{ message: string; user: User }> => {
      console.log("Mock API - Login called with:", { username: credentials.username, password: '***' });

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      try {
        const users = getStoredUsers();
        const user = users[credentials.username];

        // Check if user exists and password matches
        if (!user || user.password !== credentials.password) {
          console.log("Mock API - Login failed: Invalid credentials");
          throw new Error('Invalid credentials');
        }

        // Create user object without password
        const { password, ...userWithoutPassword } = user;

        // Save current user
        saveCurrentUserToStorage(userWithoutPassword);

        console.log("Mock API - Login successful:", { id: userWithoutPassword.id, username: userWithoutPassword.username });
        return { message: 'Login successful', user: userWithoutPassword };
      } catch (error) {
        console.error("Mock API - Login error:", error);
        throw error;
      }
    },

    logout: async (): Promise<{ message: string }> => {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Clear current user
      saveCurrentUserToStorage(null);

      return { message: 'Logout successful' };
    },

    me: async (): Promise<User> => {
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 300));

      const currentUser = getCurrentUserFromStorage();

      if (!currentUser) {
        throw new Error('Not authenticated');
      }

      return currentUser;
    }
  },

  // Comprehensive mock API endpoints for all features
  records: {
    getAll: async () => {
      console.log("Mock API - Getting all records");
      // Return mock records data
      return [
        {
          id: '1',
          title: 'Complete Blood Count',
          date: '2023-05-15',
          recordType: 'lab_report',
          provider: 'City Medical Lab',
          description: 'Routine blood work including CBC, lipid panel, and metabolic panel',
          fileUrl: 'https://example.com/files/blood_test.pdf',
          thumbnailUrl: 'https://via.placeholder.com/100',
          tags: ['blood work', 'routine']
        },
        {
          id: '2',
          title: 'Chest X-Ray',
          date: '2023-06-20',
          recordType: 'diagnostic_image',
          provider: 'Central Radiology',
          description: 'Chest X-ray to check for pneumonia',
          fileUrl: 'https://example.com/files/xray.pdf',
          thumbnailUrl: 'https://via.placeholder.com/100',
          tags: ['imaging', 'chest']
        },
        {
          id: '3',
          title: 'Annual Physical Examination',
          date: '2023-07-10',
          recordType: 'summary',
          provider: 'Dr. Sarah Johnson',
          description: 'Annual physical examination with Dr. Johnson',
          fileUrl: 'https://example.com/files/annual_checkup.pdf',
          thumbnailUrl: 'https://via.placeholder.com/100',
          tags: ['annual', 'checkup']
        },
        {
          id: '4',
          title: 'Lipitor Prescription',
          date: '2023-08-05',
          recordType: 'prescription',
          provider: 'Dr. Michael Chen',
          description: 'Prescription for Lipitor 20mg, take once daily',
          fileUrl: 'https://example.com/files/lipitor.pdf',
          thumbnailUrl: 'https://via.placeholder.com/100',
          tags: ['medication', 'cholesterol']
        },
        {
          id: '5',
          title: 'Allergy Test Results',
          date: '2023-09-12',
          recordType: 'lab_report',
          provider: 'Allergy Specialists Inc.',
          description: 'Comprehensive allergy panel testing for common allergens',
          fileUrl: 'https://example.com/files/allergy_test.pdf',
          thumbnailUrl: 'https://via.placeholder.com/100',
          tags: ['allergy', 'test']
        }
      ];
    },

    upload: async (formData: FormData) => {
      console.log("Mock API - Uploading record:", formData.get('title'));

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate a random ID for the new record
      const newId = Math.floor(Math.random() * 1000).toString();

      return {
        id: newId,
        title: formData.get('title') as string || 'New Record',
        date: new Date().toISOString().split('T')[0],
        recordType: formData.get('recordType') as string || 'summary',
        provider: formData.get('provider') as string || 'Unknown Provider',
        description: formData.get('description') as string || '',
        fileUrl: 'https://example.com/files/new_record.pdf',
        thumbnailUrl: 'https://via.placeholder.com/100',
        tags: []
      };
    },

    delete: async (recordId: string) => {
      console.log("Mock API - Deleting record:", recordId);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return { success: true, message: 'Record deleted successfully' };
    }
  },

  appointments: {
    getAll: async () => {
      console.log("Mock API - Getting all appointments");
      // Return mock appointments data
      return [
        {
          id: '1',
          title: 'Annual Checkup with Dr. Smith',
          date: '2023-08-15',
          time: '10:00 AM',
          type: 'Checkup',
          doctor: 'Dr. John Smith',
          location: 'City Medical Center',
          notes: 'Bring list of current medications',
          status: 'confirmed'
        },
        {
          id: '2',
          title: 'Dermatology Consultation',
          date: '2023-09-20',
          time: '2:30 PM',
          type: 'Consultation',
          doctor: 'Dr. Emily Johnson',
          location: 'Dermatology Associates',
          notes: 'Discuss skin condition on left arm',
          status: 'scheduled'
        },
        {
          id: '3',
          title: 'Dental Cleaning',
          date: '2023-10-05',
          time: '9:15 AM',
          type: 'Dental',
          doctor: 'Dr. Robert Chen, DDS',
          location: 'Bright Smile Dental',
          notes: 'Regular 6-month cleaning',
          status: 'confirmed'
        },
        {
          id: '4',
          title: 'Physical Therapy Session',
          date: '2023-10-12',
          time: '3:00 PM',
          type: 'Therapy',
          doctor: 'Sarah Williams, PT',
          location: 'Active Recovery Physical Therapy',
          notes: 'Bring comfortable clothes',
          status: 'scheduled'
        }
      ];
    },

    create: async (appointmentData: any) => {
      console.log("Mock API - Creating appointment:", appointmentData);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 800));

      // Generate a random ID for the new appointment
      const newId = Math.floor(Math.random() * 1000).toString();

      return {
        id: newId,
        ...appointmentData,
        status: 'scheduled'
      };
    },

    updateStatus: async (appointmentId: string, status: string) => {
      console.log("Mock API - Updating appointment status:", appointmentId, status);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        message: 'Appointment status updated successfully',
        appointmentId,
        status
      };
    }
  },

  healthMetrics: {
    getAll: async (timeRange: string = '3m') => {
      console.log("Mock API - Getting health metrics for time range:", timeRange);

      // Generate dates for the selected time range
      const today = new Date();
      let startDate = new Date();

      switch(timeRange) {
        case '1m':
          startDate.setMonth(today.getMonth() - 1);
          break;
        case '3m':
          startDate.setMonth(today.getMonth() - 3);
          break;
        case '6m':
          startDate.setMonth(today.getMonth() - 6);
          break;
        case '1y':
          startDate.setFullYear(today.getFullYear() - 1);
          break;
        case 'all':
          startDate.setFullYear(today.getFullYear() - 2);
          break;
      }

      // Generate data points between start date and today
      const bloodPressureData = [];
      const bloodSugarData = [];
      const weightData = [];

      let currentDate = new Date(startDate);

      while (currentDate <= today) {
        // Generate random values with some trend
        const daysPassed = Math.floor((currentDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
        const trendFactor = Math.sin(daysPassed / 30) * 5; // Creates a sine wave pattern

        // Blood pressure (systolic/diastolic)
        bloodPressureData.push({
          date: new Date(currentDate).toISOString().split('T')[0],
          systolic: Math.floor(125 - trendFactor + Math.random() * 10),
          diastolic: Math.floor(82 - trendFactor * 0.5 + Math.random() * 5)
        });

        // Blood sugar
        bloodSugarData.push({
          date: new Date(currentDate).toISOString().split('T')[0],
          value: Math.floor(105 - trendFactor * 0.8 + Math.random() * 8)
        });

        // Weight
        weightData.push({
          date: new Date(currentDate).toISOString().split('T')[0],
          value: 165 - daysPassed * 0.02 + trendFactor * 0.3 + Math.random() * 0.5
        });

        // Move to next data point (every 7 days)
        currentDate.setDate(currentDate.getDate() + 7);
      }

      return {
        bloodPressure: {
          latest: "128/82",
          change: -4,
          data: bloodPressureData
        },
        bloodSugar: {
          latest: "105 mg/dL",
          change: -2,
          data: bloodSugarData
        },
        weight: {
          latest: "165 lbs / 27.2",
          change: -1.3,
          data: weightData
        }
      };
    },

    add: async (metricData: any) => {
      console.log("Mock API - Adding health metric:", metricData);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 600));

      return {
        success: true,
        message: 'Health metric added successfully',
        id: Math.floor(Math.random() * 1000).toString(),
        ...metricData,
        date: new Date().toISOString()
      };
    }
  },

  family: {
    getMembers: async () => {
      console.log("Mock API - Getting family members");

      return [
        {
          id: '1',
          name: 'Sarah Johnson',
          relationship: 'Spouse',
          dateOfBirth: '1985-06-12',
          gender: 'Female',
          bloodType: 'A+',
          allergies: ['Penicillin', 'Peanuts'],
          conditions: ['Asthma'],
          medications: ['Albuterol inhaler']
        },
        {
          id: '2',
          name: 'Michael Johnson',
          relationship: 'Child',
          dateOfBirth: '2010-03-24',
          gender: 'Male',
          bloodType: 'O+',
          allergies: ['Dust mites'],
          conditions: [],
          medications: []
        },
        {
          id: '3',
          name: 'Robert Smith',
          relationship: 'Father',
          dateOfBirth: '1955-11-08',
          gender: 'Male',
          bloodType: 'B+',
          allergies: [],
          conditions: ['Hypertension', 'Type 2 Diabetes'],
          medications: ['Lisinopril', 'Metformin']
        }
      ];
    },

    addMember: async (memberData: any) => {
      console.log("Mock API - Adding family member:", memberData);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 700));

      return {
        success: true,
        message: 'Family member added successfully',
        id: Math.floor(Math.random() * 1000).toString(),
        ...memberData
      };
    },

    updateMember: async (memberId: string, memberData: any) => {
      console.log("Mock API - Updating family member:", memberId, memberData);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        message: 'Family member updated successfully',
        id: memberId,
        ...memberData
      };
    },

    deleteMember: async (memberId: string) => {
      console.log("Mock API - Deleting family member:", memberId);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return {
        success: true,
        message: 'Family member deleted successfully'
      };
    }
  },

  aiAssistant: {
    sendMessage: async (message: string) => {
      console.log("Mock API - Sending message to AI:", message);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1200));

      // Simple response logic based on keywords in the message
      let response = "I'm sorry, I don't understand your question. Could you please rephrase it?";

      if (message.toLowerCase().includes("headache")) {
        response = "Headaches can be caused by various factors including stress, dehydration, lack of sleep, or eye strain. For occasional headaches, rest, hydration, and over-the-counter pain relievers may help. If you're experiencing severe or persistent headaches, please consult with your healthcare provider.";
      } else if (message.toLowerCase().includes("cold") || message.toLowerCase().includes("flu")) {
        response = "Common cold and flu symptoms include fever, cough, sore throat, body aches, and fatigue. Rest, hydration, and over-the-counter medications can help manage symptoms. If symptoms are severe or persist for more than a week, please consult with your healthcare provider.";
      } else if (message.toLowerCase().includes("diet") || message.toLowerCase().includes("nutrition")) {
        response = "A balanced diet typically includes a variety of fruits, vegetables, whole grains, lean proteins, and healthy fats. The specific dietary needs can vary based on individual health conditions, age, and activity level. Consider consulting with a registered dietitian for personalized nutrition advice.";
      } else if (message.toLowerCase().includes("exercise") || message.toLowerCase().includes("workout")) {
        response = "Regular physical activity is important for overall health. The CDC recommends at least 150 minutes of moderate-intensity aerobic activity or 75 minutes of vigorous-intensity activity per week, along with muscle-strengthening activities on 2 or more days per week. Always consult with your healthcare provider before starting a new exercise program.";
      } else if (message.toLowerCase().includes("sleep")) {
        response = "Adults typically need 7-9 hours of sleep per night. Good sleep hygiene includes maintaining a regular sleep schedule, creating a restful environment, limiting screen time before bed, and avoiding caffeine and large meals close to bedtime. If you're experiencing persistent sleep problems, consider consulting with a healthcare provider.";
      }

      return {
        id: Math.floor(Math.random() * 1000).toString(),
        message: response,
        timestamp: new Date().toISOString()
      };
    }
  },

  profile: {
    get: async () => {
      console.log("Mock API - Getting user profile");

      const currentUser = getCurrentUserFromStorage();

      if (!currentUser) {
        throw new Error('Not authenticated');
      }

      // Add additional profile information
      return {
        ...currentUser,
        phoneNumber: '************',
        address: '123 Main St, Anytown, USA',
        emergencyContact: {
          name: 'Sarah Johnson',
          relationship: 'Spouse',
          phoneNumber: '************'
        },
        medicalInfo: {
          bloodType: 'O+',
          allergies: ['Penicillin'],
          conditions: ['Hypertension'],
          medications: ['Lisinopril 10mg daily']
        }
      };
    },

    update: async (profileData: any) => {
      console.log("Mock API - Updating user profile:", profileData);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 800));

      const currentUser = getCurrentUserFromStorage();

      if (!currentUser) {
        throw new Error('Not authenticated');
      }

      // Update the current user with new profile data
      const updatedUser = {
        ...currentUser,
        ...profileData
      };

      saveCurrentUserToStorage(updatedUser);

      return {
        success: true,
        message: 'Profile updated successfully',
        user: updatedUser
      };
    },

    updateAvatar: async (avatarUrl: string) => {
      console.log("Mock API - Updating avatar:", avatarUrl);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      const currentUser = getCurrentUserFromStorage();

      if (!currentUser) {
        throw new Error('Not authenticated');
      }

      // Update the current user with new avatar
      const updatedUser = {
        ...currentUser,
        avatarUrl
      };

      saveCurrentUserToStorage(updatedUser);

      return {
        success: true,
        message: 'Avatar updated successfully',
        user: updatedUser
      };
    }
  },

  emergency: {
    getQRCode: async () => {
      console.log("Mock API - Getting emergency QR code");

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 600));

      return {
        qrCodeUrl: 'https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=https://medikey.example.com/emergency/12345',
        emergencyAccessUrl: 'https://medikey.example.com/emergency/12345',
        expiresAt: new Date(Date.now() + 86400000).toISOString() // 24 hours from now
      };
    },

    updateInfo: async (emergencyInfo: any) => {
      console.log("Mock API - Updating emergency info:", emergencyInfo);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 700));

      return {
        success: true,
        message: 'Emergency information updated successfully'
      };
    }
  },

  smartwatch: {
    getConnectedDevices: async () => {
      console.log("Mock API - Getting connected smartwatch devices");

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 500));

      return [
        {
          id: 'sw-001',
          name: 'Apple Watch Series 7',
          model: 'Apple Watch',
          lastSynced: '2023-10-15T14:30:00Z',
          batteryLevel: 72,
          status: 'connected'
        },
        {
          id: 'sw-002',
          name: 'Fitbit Sense',
          model: 'Fitbit',
          lastSynced: '2023-10-14T09:15:00Z',
          batteryLevel: 45,
          status: 'disconnected'
        }
      ];
    },

    sendCommand: async (deviceId: string, command: string, data: any = {}) => {
      console.log("Mock API - Sending command to smartwatch:", deviceId, command, data);

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 800));

      return {
        success: true,
        message: `Command ${command} sent successfully to device ${deviceId}`,
        result: {
          acknowledged: true,
          timestamp: new Date().toISOString()
        }
      };
    }
  }
};

// Export a function to check if we should use mock API
export const shouldUseMockApi = (): boolean => {
  return window.location.hostname.includes('github.io');
};

import React from "react";
import { <PERSON><PERSON>, ButtonProps } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface AnimatedButtonProps extends ButtonProps {
  animationType?: "pulse" | "bounce" | "shake" | "glow" | "none";
  animationDelay?: number;
  animationDuration?: number;
  animationTrigger?: "hover" | "always";
  children: React.ReactNode;
}

export function AnimatedButton({
  animationType = "none",
  animationDelay = 0,
  animationDuration = 1,
  animationTrigger = "hover",
  children,
  className,
  ...props
}: AnimatedButtonProps) {
  const getAnimationClass = () => {
    if (animationType === "none") return "";

    const baseClass = {
      pulse: "animate-pulse",
      bounce: "animate-bounce",
      shake: "animate-shake",
      glow: "animate-glow",
    }[animationType];

    if (animationTrigger === "always") {
      return baseClass;
    } else {
      return `hover:${baseClass}`;
    }
  };

  // Define custom animations
  const customAnimations = `
    @keyframes shake {
      0%, 100% { transform: translateX(0); }
      10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
      20%, 40%, 60%, 80% { transform: translateX(2px); }
    }
    @keyframes glow {
      0%, 100% { box-shadow: 0 0 5px rgba(var(--primary), 0.5); }
      50% { box-shadow: 0 0 20px rgba(var(--primary), 0.8); }
    }
    .animate-shake {
      animation: shake ${animationDuration}s cubic-bezier(0.36, 0.07, 0.19, 0.97) infinite;
      animation-delay: ${animationDelay}s;
    }
    .animate-glow {
      animation: glow ${animationDuration}s ease-in-out infinite;
      animation-delay: ${animationDelay}s;
    }
  `;

  return (
    <>
      <style>{customAnimations}</style>
      <Button
        className={cn(
          "transition-all duration-300",
          getAnimationClass(),
          className
        )}
        {...props}
      >
        {children}
      </Button>
    </>
  );
}
